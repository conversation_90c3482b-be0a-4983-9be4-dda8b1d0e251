## NedBank Android [![CircleCI](https://circleci.com/gh/greenbank60days/banking-app-droid.svg?style=svg&circle-token=****************************************)](https://circleci.com/gh/greenbank60days/banking-app-droid)

#### Environment:
- Android Studio 3.0
- Java 1.8 or higher
- Minimal supported Android version - API 17 (4.2)

#### Architecture
- MVP with clean code

#### Guidelines
###### Adding new library to project
If adding new library, please add it's version to top build.gradle file, into **ext** section

>
>     // Define versions in a single place
>     ext {
>         // some libraries
>         rxlifecycleVersion = '2.1.0'
>         // more libraries
>     }

Then in selected build.gradle file, you can reference it

>		compile "com.trello.rxlifecycle2:rxlifecycle:$rootProject.rxlifecycleVersion"
>

In this way, we'll use the same version of library, across all modules.

#### Libraries:
- [Retrofit2](https://github.com/square/retrofit/) - network client
- [<PERSON><PERSON>](https://github.com/square/moshi) - json library
- [Retrofit2 Moshi Converter](https://github.com/square/retrofit/tree/master/retrofit-converters/moshi) - moshi support in retrofit2
- [Picasso](https://github.com/square/picasso) - image downloading/caching
- [RxJava2](https://github.com/ReactiveX/RxJava) - reactive programming
- [RxBinding2](https://github.com/JakeWharton/RxBinding) - Android UI binding for RxJava
- [RxLifecycle2](https://github.com/trello/RxLifecycle) - lifecycle handling in RxJava apps
- [RxPermissions](https://github.com/tbruyelle/RxPermissions) - library for checking permissions
- [RxBroadcast](https://github.com/cantrowitz/RxBroadcast) - library for listening to broadcast messages
- [Dagger2](https://github.com/google/dagger) - dependency injection

#### Testing:
- [Espresso](https://google.github.io/android-testing-support-library/docs/espresso/)
- [Mockito](https://github.com/mockito/mockito)
- [Junit](http://junit.org/junit4/)


package za.co.nedbank.services.insurance.view.vvap.policy_admin.banking.edit_banking_detail

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.TypedValue
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.appcompat.widget.AppCompatRadioButton
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.jakewharton.rxbinding2.widget.RxTextView
import za.co.nedbank.core.base.NBBaseActivity
import za.co.nedbank.core.tracking.InsuranceTrackingEvent.EVENT_MANAGE_POLICY_DONE
import za.co.nedbank.core.tracking.InsuranceTrackingEvent.EVENT_MANAGE_POLICY_NEXT_REVIEW
import za.co.nedbank.core.tracking.InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_BANKING
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.utils.CollectionUtils
import za.co.nedbank.core.utils.FormattingUtil
import za.co.nedbank.core.utils.StringUtils
import za.co.nedbank.core.utils.ViewUtils
import za.co.nedbank.core.view.accounts.listener.IViewHolderInteraction
import za.co.nedbank.core.view.accounts.ui.AccountsViewFragment
import za.co.nedbank.core.view.banklist.model.BankBranchViewModel
import za.co.nedbank.core.view.banklist.model.BankViewModel
import za.co.nedbank.core.view.model.AccountViewModel
import za.co.nedbank.services.Constants
import za.co.nedbank.services.R
import za.co.nedbank.services.databinding.ActivityEditBankingDetailsBinding
import za.co.nedbank.services.di.ServicesDI
import za.co.nedbank.services.insurance.view.generic.common.other.model.InsuranceCodeDescriptionViewModel
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.helper.InsuranceHelperClass
import za.co.nedbank.services.insurance.view.other.listeners.generic.ISelectedViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.BankAccountViewModel
import za.co.nedbank.services.insurance.view.other.utils.parcelableObject
import za.co.nedbank.services.insurance.view.vvap.policy_admin.other.helper.EditBankingDetailsHelper
import za.co.nedbank.uisdk.component.CompatButton
import za.co.nedbank.uisdk.component.CompatEditText
import za.co.nedbank.uisdk.component.CompatPicker
import za.co.nedbank.uisdk.component.CompatTextViewEnhanced
import javax.inject.Inject

class PolicyAdminEditBankingDetailsActivity : NBBaseActivity(), PolicyAdminEditBankingDetailsView,
    View.OnClickListener, IViewHolderInteraction {

    private lateinit var mToolbar: Toolbar
    private lateinit var mPolicyNumber: String
    private lateinit var mMainView: LinearLayout
    private lateinit var mSaveButton: CompatButton
    private lateinit var mProgressBar: ProgressBar
    private lateinit var mRiskSerialNumber: String
    private lateinit var mCbkConsent: AppCompatCheckBox
    private lateinit var mSelectBankPicker: CompatPicker
    private lateinit var mBranchCodePicker: CompatPicker
    private lateinit var mAccountTypePicker: CompatPicker
    private lateinit var mRlNedbankLayout: RelativeLayout
    private lateinit var mEdtAccountNumber: CompatEditText
    private lateinit var mRbOtherRdb: AppCompatRadioButton
    private lateinit var mOtherBankContainer: LinearLayout
    private lateinit var mRlOtherBankLayout: RelativeLayout
    private lateinit var mDiscountTV: CompatTextViewEnhanced
    private lateinit var mRbNedbankRdb: AppCompatRadioButton
    private lateinit var mNedbankTxtTv: CompatTextViewEnhanced
    private lateinit var mNedbankViewContainer: RelativeLayout
    private lateinit var mFuneralInfoContainer: LinearLayout
    private lateinit var mMonthlyPremium: CompatTextViewEnhanced
    private lateinit var mAnnualMonthlyTitle: CompatTextViewEnhanced

    private val binding get() = _binding
    private var subProduct: String? = null
    private var isPolicyPL: Boolean = false
    private var mBankViewModel: BankViewModel? = null
    private var isBankingDetailsUpdated: Boolean = false
    private var mCarousalSelectedPos: Int? = Constants.ZERO
    private var mBankBranchData: BankBranchViewModel? = null
    private var mSelectedBankDetail: BankAccountViewModel? = null
    private var mAccountsViewFragment: AccountsViewFragment? = null
    private var mAccountTypeList: List<ISelectedViewModel?>? = null
    private var _binding: ActivityEditBankingDetailsBinding? = null
    private var mNedbankAccountList: List<AccountViewModel?>? = null
    private var mSelectedAccountTypeModel: InsuranceCodeDescriptionViewModel? = null
    private var accountViewModel =
        za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel()

    @Inject
    lateinit var presenter: PolicyAdminEditBankingDetailsPresenter

    @Inject
    lateinit var mHelperErrorClass: InsuranceHelperClass

    override fun onCreate(savedInstanceState: Bundle?) {
        ServicesDI.getActivityComponent(this).inject(this)
        super.onCreate(savedInstanceState)
        _binding = ActivityEditBankingDetailsBinding.inflate(layoutInflater)
        setContentView(binding!!.root)
        presenter.bind(this)
        getIntentData()
        initializeVariables()
    }

    private fun getIntentData() {
        if (intent != null) {
            mPolicyNumber =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_VVAP_POLICY_NUMBER)!!
            mRiskSerialNumber =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_VVAP_RISK_SERIAL_NUMBER)!!
            mSelectedBankDetail =
                intent.parcelableObject(InsuranceConstants.ParamKeys.PARAM_EDIT_BANKING_DETAILS)
            subProduct =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME)
            isPolicyPL =
                intent.getBooleanExtra(InsuranceConstants.ParamKeys.PARAM_IS_PL_FLOW, false)
            accountViewModel =
                intent.parcelableObject(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS)!!
        }
    }

    private fun initializeVariables() {

        //initializing all the variables
        mToolbar = binding!!.toolbar
        mSaveButton = binding!!.saveBtn
        mMainView = binding!!.llMainView
        mDiscountTV = binding!!.tvDiscount
        mRbNedbankRdb = binding!!.rbNedBank
        mNedbankTxtTv = binding!!.tvNedBank
        mRbOtherRdb = binding!!.rbOtherBank
        mCbkConsent = binding!!.chkBoxConsent
        mRlNedbankLayout = binding!!.rlNedBank
        mSelectBankPicker = binding!!.bankPicker
        mProgressBar = binding!!.progressBarView
        mRlOtherBankLayout = binding!!.rlOtherBank
        mEdtAccountNumber = binding!!.edtAccountNumber
        mBranchCodePicker = binding!!.branchCodePicker
        mAccountTypePicker = binding!!.accountTypePicker
        mMonthlyPremium = binding!!.tvAnnualMonthlyPremium
        mAnnualMonthlyTitle = binding!!.tvAnnualMonthlyTitle
        mNedbankViewContainer = binding!!.rlNedBankContainer
        mFuneralInfoContainer = binding!!.llFuneralInfo
        mOtherBankContainer = binding!!.llOtherBankContainer

        // set toolbar
        initToolbar(mToolbar, true, false)
        mToolbar.title = getString(R.string.vvap_banking_edit_title)

        // Initially disable checkbox - user must change fields first
        mCbkConsent.isEnabled = false

        // set click listeners
        mSaveButton.setOnClickListener(this)
        mRbOtherRdb.setOnClickListener(this)
        mCbkConsent.setOnClickListener(this)
        mRbNedbankRdb.setOnClickListener(this)
        mRlNedbankLayout.setOnClickListener(this)
        mSelectBankPicker.setOnClickListener(this)
        mBranchCodePicker.setOnClickListener(this)
        mRlOtherBankLayout.setOnClickListener(this)
        mAccountTypePicker.setOnClickListener(this)

        mAccountsViewFragment =
            supportFragmentManager.findFragmentById(R.id.fragmentAccounts) as AccountsViewFragment
        setNedbankViewLabel()
        mAccountsViewFragment?.setBackgroundColor(
            ContextCompat.getColor(
                this, R.color.white
            )
        )
        presenter.fetchNedbankAccountAndType()

        if (isPolicyPL) {
            mSaveButton.text = getString(R.string.insurance_next)
            ViewUtils.showViews(mAnnualMonthlyTitle, mMonthlyPremium)
            mMonthlyPremium.text = FormattingUtil.convertToSouthAfricaFormattedCurrency(
                accountViewModel.premiumAmount!!
            )
            setDiscountLabel()
        }
    }

    private fun setDiscountLabel() {
        if (mSelectedBankDetail?.routingNumberId.equals(
                InsuranceConstants.RegisterClaimConstants.UNIVERSAL_BRANCH_NAME, ignoreCase = true
            )
        ) {
            mDiscountTV.text = getString(R.string.pl_edit_banking_with_discount)
        } else {
            mDiscountTV.text = getString(R.string.pl_edit_banking_discount)
        }
        ViewUtils.showViews(mDiscountTV)
    }

    private fun setNedbankViewLabel() {
        val labelView =
            mAccountsViewFragment?.view?.findViewById<CompatTextViewEnhanced>(R.id.txvAccountLabel)
        if (labelView != null) {
            val marginPx = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, Constants.TWENTY.toFloat(), resources.displayMetrics
            ).toInt()
            val params = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
            )
            params.setMargins(marginPx, marginPx, marginPx, Constants.ZERO)
            labelView.layoutParams = params
            val paddingPx = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, Constants.EIGHT.toFloat(), resources.displayMetrics
            )
            labelView.setPadding(
                paddingPx.toInt(), paddingPx.toInt(), paddingPx.toInt(), paddingPx.toInt()
            )
        }
    }

    private fun checkIfNedbankAccountExistOrNot() {
        if (CollectionUtils.isEmpty(mNedbankAccountList)) {
            mRbNedbankRdb.isClickable = false
            mRbNedbankRdb.isEnabled = false
            mRbNedbankRdb.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.radiobutton_disable_background, 0, 0, 0
            )
            mNedbankTxtTv.setTextColor(ContextCompat.getColor(this, R.color.disable_gray))
            mRlNedbankLayout.isClickable = false
            mRlNedbankLayout.isEnabled = false
        }
    }

    @SuppressLint("CheckResult")
    private fun onTextChanges() {
        RxTextView.textChanges(mEdtAccountNumber.inputField).subscribe {
            if (mEdtAccountNumber.hasError()) {
                mEdtAccountNumber.clearErrors()
            }
            if (mEdtAccountNumber.hasFocus()) {
                isBankingDetailsUpdated = true
                enableCheckboxIfFieldsChanged()
            }
            validateInputFields()
        }
    }

    private fun validateInputFields() {
        if (mRbNedbankRdb.isChecked && mCbkConsent.isChecked && isBankingDetailsUpdated) {
            mSaveButton.isEnabled = true
        } else {
            presenter.validateAllFields(
                mSelectBankPicker,
                mBranchCodePicker,
                mAccountTypePicker,
                mEdtAccountNumber,
                mCbkConsent.isChecked,
                isBankingDetailsUpdated
            )
        }
    }

    private fun showAccounts(accountViewModels: List<AccountViewModel?>?) {
        if (mAccountsViewFragment != null) {
            if (CollectionUtils.isNotEmpty(accountViewModels)) {
                mAccountsViewFragment?.refreshViews(accountViewModels, true)
            } else {
                mAccountsViewFragment?.refreshViews(
                    accountViewModels, true, getString(R.string.no_accounts_available)
                )
            }
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.bankPicker -> {
                presenter.navigateToBankingListScreen()
            }

            R.id.branchCodePicker -> {
                presenter.navigateToBranchListScreen(mBankViewModel?.branchList)
            }

            R.id.accountTypePicker -> {
                presenter.handleAccountTypeClick()
            }

            R.id.rlNedBank, R.id.rbNedBank -> {
                // For funeral products, radio buttons should not be interactive
                if (!isFuneralProduct()) {
                    isBankingDetailsUpdated = true
                    enableCheckboxIfFieldsChanged()
                    resetFormValues()
                    setNedbankSelection()
                    validateInputFields()
                }
            }

            R.id.rlOtherBank, R.id.rbOtherBank -> {
                // For funeral products, radio buttons should not be interactive
                if (!isFuneralProduct()) {
                    isBankingDetailsUpdated = true
                    enableCheckboxIfFieldsChanged()
                    resetFormValues()
                    setOtherBankSelection()
                    validateInputFields()
                }
            }

            R.id.saveBtn -> {
                if (mSaveButton.isEnabled) {
                    if (mRbNedbankRdb.isChecked) {
                        moveToNext()
                    } else {
                        presenter.verifyAccountNo()
                    }
                }
            }

            R.id.chkBoxConsent -> {
                validateInputFields()
                mEdtAccountNumber.clearFocus()
                mEdtAccountNumber.clearErrors()
            }
        }
    }

    private fun setOtherBankSelection() {
        mRbOtherRdb.isChecked = true
        mRbNedbankRdb.isChecked = false
        ViewUtils.showViews(mOtherBankContainer, mSaveButton, mCbkConsent)
        ViewUtils.hideViews(mNedbankViewContainer, mFuneralInfoContainer)
    }

    private fun setNedbankSelection() {
        mRbOtherRdb.isChecked = false
        mRbNedbankRdb.isChecked = true

        // For funeral products on Nedbank accounts, hide radio buttons and show info text
        if (isFuneralProduct()) {
            ViewUtils.hideViews(mRbNedbankRdb, mRbOtherRdb, mRlNedbankLayout, mRlOtherBankLayout)
            // Show informational text instead of radio buttons for funeral products
            ViewUtils.showViews(mFuneralInfoContainer, mNedbankViewContainer, mCbkConsent, mSaveButton)
        } else {
            ViewUtils.hideViews(mFuneralInfoContainer)
            ViewUtils.showViews(mNedbankViewContainer, mCbkConsent, mSaveButton)
        }

        ViewUtils.hideViews(
            mOtherBankContainer, mAccountTypePicker, mBranchCodePicker, mEdtAccountNumber
        )
    }

    private fun getSelectedAccountDetail(): AccountViewModel {
        return mAccountsViewFragment!!.provideSelectedAccount()
    }

    /**
     * Check if the current product is a funeral product (10K, 30K, Family)
     */
    private fun isFuneralProduct(): Boolean {
        val productId = accountViewModel?.productId
        return productId in listOf(
            InsuranceConstants.MYCOVER_FUNERAL_INDIVIDUAL_10K_CODE,
            InsuranceConstants.MYCOVER_FUNERAL_INDIVIDUAL_30K_CODE,
            InsuranceConstants.MYCOVER_FUNERAL_FAMILY_CODE
        )
    }

    /**
     * DEMO/TEST METHOD: Test 10K funeral product behavior
     * This temporarily sets the product ID to a 10K funeral product for testing
     */
    private fun setTest10KFuneralProduct() {
        accountViewModel?.productId = InsuranceConstants.MYCOVER_FUNERAL_INDIVIDUAL_10K_CODE
        setNedbankSelection()
        showTestMessage("Testing 10K Funeral Product - Please note section should be visible")
    }

    /**
     * DEMO/TEST METHOD: Test 30K funeral product behavior
     */
    private fun setTest30KFuneralProduct() {
        accountViewModel?.productId = InsuranceConstants.MYCOVER_FUNERAL_INDIVIDUAL_30K_CODE
        setNedbankSelection()
        showTestMessage("Testing 30K Funeral Product - Please note section should be visible")
    }

    /**
     * DEMO/TEST METHOD: Test Family funeral product behavior
     */
    private fun setTestFamilyFuneralProduct() {
        accountViewModel?.productId = InsuranceConstants.MYCOVER_FUNERAL_FAMILY_CODE
        setNedbankSelection()
        showTestMessage("Testing Family Funeral Product - Please note section should be visible")
    }

    /**
     * DEMO/TEST METHOD: Reset to non-funeral product (VVAP)
     */
    private fun setTestVVAPProduct() {
        accountViewModel?.productId = "VVAP_TEST" // Non-funeral product
        // Reset to normal behavior - show radio buttons
        ViewUtils.showViews(mRbNedbankRdb, mRbOtherRdb, mRlNedbankLayout, mRlOtherBankLayout)
        ViewUtils.hideViews(mFuneralInfoContainer)
        setNedbankSelection()
        showTestMessage("Testing VVAP Product - Radio buttons should be visible, Please note hidden")
    }

    /**
     * DEMO/TEST METHOD: Reset to PL product
     */
    private fun setTestPLProduct() {
        accountViewModel?.productId = "PL_TEST" // Non-funeral product
        isPolicyPL = true
        // Reset to normal behavior - show radio buttons
        ViewUtils.showViews(mRbNedbankRdb, mRbOtherRdb, mRlNedbankLayout, mRlOtherBankLayout)
        ViewUtils.hideViews(mFuneralInfoContainer)
        setNedbankSelection()
        showTestMessage("Testing PL Product - Radio buttons should be visible, Please note hidden")
    }

    /**
     * Helper method to show test messages
     */
    private fun showTestMessage(message: String) {
        // You can use Toast or Log for testing feedback
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_LONG).show()
        android.util.Log.d("FUNERAL_TEST", message)
        logCurrentState()
    }

    /**
     * DEMO/TEST METHOD: Log current UI state for debugging
     */
    private fun logCurrentState() {
        val productId = accountViewModel?.productId
        val isFuneral = isFuneralProduct()
        val funeralInfoVisible = mFuneralInfoContainer.visibility == View.VISIBLE
        val radioButtonsVisible = mRbNedbankRdb.visibility == View.VISIBLE

        android.util.Log.d("FUNERAL_TEST", "=== Current State ===")
        android.util.Log.d("FUNERAL_TEST", "Product ID: $productId")
        android.util.Log.d("FUNERAL_TEST", "Is Funeral Product: $isFuneral")
        android.util.Log.d("FUNERAL_TEST", "Funeral Info Container Visible: $funeralInfoVisible")
        android.util.Log.d("FUNERAL_TEST", "Radio Buttons Visible: $radioButtonsVisible")
        android.util.Log.d("FUNERAL_TEST", "isPolicyPL: $isPolicyPL")
        android.util.Log.d("FUNERAL_TEST", "==================")
    }

    override fun moveToNext() {
        val bankingAccountViewModel: BankAccountViewModel
        if (mRbOtherRdb.isChecked) {
            bankingAccountViewModel = BankAccountViewModel(
                bankName = mBankViewModel?.bankName,
                accountNumberId = mEdtAccountNumber.value,
                typeCode = mSelectedAccountTypeModel?.description,
                routingNumberId = if (StringUtils.isNotEmpty(mBankViewModel?.universalCode)) mBankViewModel?.universalCode else mBankBranchData?.branchCode
            )
        } else {
            //Nedbank account selection
            val nedbankAccountDetails = getSelectedAccountDetail()
            bankingAccountViewModel = BankAccountViewModel(
                bankName = InsuranceConstants.NEDBANK_BANK_NAME,
                accountNumberId = nedbankAccountDetails.accountNumber,
                typeCode = if (nedbankAccountDetails.accountType.equals(
                        InsuranceConstants.IssuePolicyConstants.CA_ACCOUNT, ignoreCase = true
                    )
                ) {
                    InsuranceConstants.IssuePolicyConstants.DEBIT_ACCOUNT
                } else {
                    InsuranceConstants.IssuePolicyConstants.SAVING_ACCOUNT
                },
                routingNumberId = InsuranceConstants.RegisterClaimConstants.UNIVERSAL_BRANCH_NAME
            )
        }

        if (isPolicyPL) {
            val isNedbankOldSelection = mSelectedBankDetail?.routingNumberId.equals(
                InsuranceConstants.RegisterClaimConstants.UNIVERSAL_BRANCH_NAME, ignoreCase = true
            )
            val isNedbankNewSelection = mRbNedbankRdb.isChecked
            val helper = EditBankingDetailsHelper(
                riskSerialNumber = mRiskSerialNumber,
                policyNumber = mPolicyNumber,
                oldPremium = accountViewModel.premiumAmount!!.toFloat(),
                newPremium = accountViewModel.premiumAmount!!.toFloat(),
                isNedbankOldSelection = isNedbankOldSelection,
                isNedbankNewSelection = isNedbankNewSelection,
                bankingAccountViewModel
            )

            if ((isNedbankNewSelection && !isNedbankOldSelection) || (!isNedbankNewSelection && isNedbankOldSelection)) {
                presenter.fetchPremium(helper, mPolicyNumber, mRiskSerialNumber)
            } else {
                helper.newPremium = helper.oldPremium
                setPremiumAmount(helper)
            }
        } else {
            presenter.updateBankingDetails(
                EditBankingDetailsHelper(
                    riskSerialNumber = mRiskSerialNumber,
                    policyNumber = mPolicyNumber,
                    oldPremium = accountViewModel.premiumAmount!!.toFloat(),
                    newPremium = accountViewModel.premiumAmount!!.toFloat(),
                    isNedbankOldSelection = mSelectedBankDetail?.routingNumberId.equals(
                        InsuranceConstants.RegisterClaimConstants.UNIVERSAL_BRANCH_NAME,
                        ignoreCase = true
                    ),
                    isNedbankNewSelection = mRbNedbankRdb.isChecked,
                    bankingAccountViewModel
                ),
                mPolicyNumber,
                mRiskSerialNumber,
            )
        }
    }

    override fun setPremiumAmount(helper: EditBankingDetailsHelper) {
        presenter.sendEventWithProduct(
            EVENT_MANAGE_POLICY_NEXT_REVIEW, subProduct,
            TrackingEvent.ANALYTICS.PL_COMBO_TITLE
        )
        presenter.showReviewPremiumScreen(helper, isPolicyPL, subProduct)
    }

    override fun getAccountNumber(): String? = mEdtAccountNumber.value

    override fun getSelectedAccountType(): ISelectedViewModel? = mSelectedAccountTypeModel

    override fun getBranchCode(): String? {
        if (StringUtils.isNotEmpty(mBankViewModel?.universalCode)) {
            return mBankViewModel?.universalCode
        }
        return mBankBranchData?.branchCode
    }

    override fun showAccountAPIError() {
        mHelperErrorClass.navigateToErrorActivity(
            { mEdtAccountNumber.showError(getString(R.string.insurance_invalid_account_no)) },
            InsuranceConstants.ErrorType.ACCOUNT_VERIFICATION_ADMIN_POLICY_ERROR
        )
    }

    override fun setNedbankAccount(insuranceAccountList: List<AccountViewModel?>?) {
        this.mNedbankAccountList = insuranceAccountList
        showAccounts(insuranceAccountList)
        onTextChanges()
        checkIfNedbankAccountExistOrNot()
        //Show main view after success response
        ViewUtils.showViews(mMainView)

        // For funeral products, automatically set Nedbank selection and hide radio buttons
        if (isFuneralProduct()) {
            setNedbankSelection()
            if (mSelectedBankDetail?.routingNumberId.equals(
                    InsuranceConstants.RegisterClaimConstants.UNIVERSAL_BRANCH_NAME, ignoreCase = true
                )
            ) {
                mCarousalSelectedPos =
                    insuranceAccountList?.indexOfFirst { it?.accountNumber.equals(mSelectedBankDetail?.accountNumberId) }
                mAccountsViewFragment?.setCurrentItem(mCarousalSelectedPos ?: Constants.ZERO)
            }
        } else {
            // Original logic for PL and VVAPS
            // in case of Nedbank selection
            if (mSelectedBankDetail?.routingNumberId.equals(
                    InsuranceConstants.RegisterClaimConstants.UNIVERSAL_BRANCH_NAME, ignoreCase = true
                )
            ) {
                setNedbankSelection()
                mCarousalSelectedPos =
                    insuranceAccountList?.indexOfFirst { it?.accountNumber.equals(mSelectedBankDetail?.accountNumberId) }
                mAccountsViewFragment?.setCurrentItem(mCarousalSelectedPos ?: Constants.ZERO)
            } else {//in case of other bank selection
                preSelectionForOtherBank()
            }
        }
    }

    private fun preSelectionForOtherBank() {
        mRbOtherRdb.isChecked = true
        mRbNedbankRdb.isChecked = false
        ViewUtils.showViews(
            mOtherBankContainer,
            mBranchCodePicker,
            mEdtAccountNumber,
            mAccountTypePicker,
            mCbkConsent,
            mSaveButton
        )
        ViewUtils.hideViews(mNedbankViewContainer)

        if (StringUtils.isNotEmpty(mSelectedBankDetail?.bankName)) {
            mSelectBankPicker.setText(mSelectedBankDetail?.bankName)
        }
        if (StringUtils.isNotEmpty(mSelectedBankDetail?.routingNumberId)) {
            mBranchCodePicker.setText(mSelectedBankDetail?.routingNumberId)
        }
        if (presenter.checkIfHavingMultipleBranch(mSelectedBankDetail?.routingNumberId)) {
            mBranchCodePicker.setDrawableRight(null)
            mBranchCodePicker.isEnabled = false
        } else {
            mBranchCodePicker.isEnabled = true
            mBranchCodePicker.setDrawableRight(
                ContextCompat.getDrawable(
                    this, R.drawable.ic_right_arrow_silver_wrapper
                )
            )
        }
        val modifyTypeCode: String? = if (mSelectedBankDetail?.typeCode.equals(
                InsuranceConstants.IssuePolicyConstants.DEBIT_ACCOUNT, ignoreCase = true
            )
        ) {
            InsuranceConstants.IssuePolicyConstants.CURRENT_ACCOUNT
        } else {
            mSelectedBankDetail?.typeCode
        }
        setPreviousAccountType(presenter.getAlreadySelectedAccountType(modifyTypeCode) as InsuranceCodeDescriptionViewModel?)

        if (StringUtils.isNotEmpty(mSelectedBankDetail?.accountNumberId)) {
            mEdtAccountNumber.setText(mSelectedBankDetail?.accountNumberId)
        }
    }

    override fun submitSuccess(helper: EditBankingDetailsHelper) {
        if (helper.isNedbankNewSelection) {
            presenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_SAVE_BANKING, subProduct,
                TrackingEvent.ANALYTICS.VVAPS_TITLE
            )
            presenter.navigateToBankingDetail(helper.accountViewModel)
        } else {
            presenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_DONE,
                subProduct,
                TrackingEvent.ANALYTICS.VVAPS_TITLE
            )
            presenter.navigateToSuccess(helper, subProduct)
        }
    }

    override fun showSubmitError(bankHelper: EditBankingDetailsHelper) {
        mHelperErrorClass.navigateToAdminManagePolicy(
            { isRetry ->
                if (isRetry) {
                    presenter.updateBankingDetails(bankHelper, mPolicyNumber, mRiskSerialNumber)
                } else {
                    presenter.moveToManagePolicyScreen()
                }
            },
            InsuranceConstants.ErrorType.INSURANCE_HOC_RETRY_API_ERROR,
            InsuranceConstants.HocScreenNameConstants.HOC_GENERAL
        )
    }

    override fun goToBankingScreenWithError() {
        presenter.navigateToBankingDetail()
    }

    override fun showProgressBar(isVisible: Boolean) {
        if (isVisible) {
            ViewUtils.showViews(mProgressBar)
            window.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
            )
        } else {
            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            ViewUtils.hideViews(mProgressBar)
        }
    }

    override fun showAccountTextError() {
        mEdtAccountNumber.showError(getString(R.string.insurance_invalid_account_no))
    }

    override fun setNextButtonEnable(isEnable: Boolean) {
        mSaveButton.isEnabled = isEnable
    }

    override fun setSelectedBank(bankViewModel: BankViewModel?) {
        isBankingDetailsUpdated = true
        enableCheckboxIfFieldsChanged()
        if (bankViewModel != null) {
            if (mBankViewModel == null || presenter.areBankNamesDifferent(
                    mBankViewModel?.bankName, bankViewModel.bankName
                )
            ) {
                resetFormValues()
            }
            this.mBankViewModel = bankViewModel
            ViewUtils.showViews(
                mBranchCodePicker, mEdtAccountNumber, mAccountTypePicker, mCbkConsent
            )

            if (StringUtils.isNotEmpty(bankViewModel.bankName)) {
                mSelectBankPicker.setText(bankViewModel.bankName)
            }
            if (StringUtils.isNotEmpty(bankViewModel.universalCode)) {
                mBranchCodePicker.setText(bankViewModel.universalCode)
                mBranchCodePicker.setDrawableRight(null)
                mBranchCodePicker.isEnabled = false
            } else {
                mBranchCodePicker.isEnabled = true
                mBranchCodePicker.setDrawableRight(
                    ContextCompat.getDrawable(
                        this, R.drawable.ic_right_arrow_silver_wrapper
                    )
                )
                mBranchCodePicker.setText(StringUtils.EMPTY_STRING)
            }
            validateInputFields()
        }
    }

    private fun resetFormValues() {
        mEdtAccountNumber.setText(StringUtils.EMPTY_STRING)
        mEdtAccountNumber.clearErrors()
        mAccountTypePicker.setText(StringUtils.EMPTY_STRING)
        mSelectBankPicker.setText(StringUtils.EMPTY_STRING)
    }

    override fun setAccountTypeList(list: List<ISelectedViewModel?>?) {
        this.mAccountTypeList = list
    }

    override fun getAccountTypeList(): List<ISelectedViewModel?>? = mAccountTypeList

    override fun setAccountTypeData(accountType: InsuranceCodeDescriptionViewModel?) {
        isBankingDetailsUpdated = true
        enableCheckboxIfFieldsChanged()
        this.mSelectedAccountTypeModel = accountType
        mAccountTypePicker.setText(accountType?.description ?: StringUtils.EMPTY_STRING)
        validateInputFields()
    }

    private fun setPreviousAccountType(accountType: InsuranceCodeDescriptionViewModel?) {
        this.mSelectedAccountTypeModel = accountType
        mAccountTypePicker.setText(accountType?.description ?: StringUtils.EMPTY_STRING)
        validateInputFields()
    }

    override fun setSelectedBranch(branchViewModel: BankBranchViewModel?) {
        isBankingDetailsUpdated = true
        enableCheckboxIfFieldsChanged()
        if (branchViewModel != null) {
            this.mBankBranchData = branchViewModel
            val branchNameCode =
                (if (StringUtils.isNotEmpty(branchViewModel.branchName)) branchViewModel.branchName
                else StringUtils.EMPTY_STRING) + if (StringUtils.isNotEmpty(branchViewModel.branchCode)) StringUtils.SPACE + StringUtils.LEFT_ROUND_PARENTHESIS + branchViewModel.branchCode + StringUtils.RIGHT_ROUND_PARENTHESIS else StringUtils.EMPTY_STRING
            mBranchCodePicker.setText(branchNameCode)
            mBranchCodePicker.isEnabled = true
            mBranchCodePicker.setDrawableRight(
                ContextCompat.getDrawable(
                    this, R.drawable.ic_right_arrow_silver_wrapper
                )
            )
            validateInputFields()
        }
    }

    override fun showAPIError() {
        mHelperErrorClass.navigateToBackErrorActivity({ isBackPressed, _ ->
            if (isBackPressed) {
                onBackPressedDispatcher.onBackPressed()
            }
        }, InsuranceConstants.ErrorType.INSURANCE_HOC_RETRY_API_ERROR)
    }

    override fun onParentViewSelected(position: Int, isFromAdapter: Boolean) {
        if (position != mCarousalSelectedPos) {
            isBankingDetailsUpdated = true
            enableCheckboxIfFieldsChanged()
        }
        validateInputFields()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_close_gray, mToolbar.menu)

        // Add demo menu items for testing funeral products
        menu.add(0, 1001, 0, "Test 10K Funeral")
        menu.add(0, 1002, 0, "Test 30K Funeral")
        menu.add(0, 1003, 0, "Test Family Funeral")
        menu.add(0, 1004, 0, "Test VVAP Product")
        menu.add(0, 1005, 0, "Test PL Product")
        menu.add(0, 1006, 0, "Log Current State")

        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        val itemId = item.itemId

        if (itemId == R.id.menu_item_close) {
            presenter.navigateToBankingDetail()
            return true
        } else if (itemId == 1001) {
            // Test 10K Funeral Product
            setTest10KFuneralProduct()
            return true
        } else if (itemId == 1002) {
            // Test 30K Funeral Product
            setTest30KFuneralProduct()
            return true
        } else if (itemId == 1003) {
            // Test Family Funeral Product
            setTestFamilyFuneralProduct()
            return true
        } else if (itemId == 1004) {
            // Test VVAP Product (non-funeral)
            setTestVVAPProduct()
            return true
        } else if (itemId == 1005) {
            // Test PL Product (non-funeral)
            setTestPLProduct()
            return true
        } else if (itemId == 1006) {
            // Log current state for debugging
            logCurrentState()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    /**
     * Enable checkbox only after user has made changes to any field
     */
    private fun enableCheckboxIfFieldsChanged() {
        if (!mCbkConsent.isEnabled && isBankingDetailsUpdated) {
            mCbkConsent.isEnabled = true
            // Update visual styling when enabled
            mCbkConsent.setTextColor(ContextCompat.getColor(this, R.color.black_333333))
            mCbkConsent.buttonTintList = ContextCompat.getColorStateList(this, R.color.color_bbbbbb)
        }
    }
}
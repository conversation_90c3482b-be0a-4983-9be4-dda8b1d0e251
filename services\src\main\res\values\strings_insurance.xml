<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="insurance">Insurance</string>

    <string name="insurance_list_title">My policies and applications</string>

    <!--Insurance common strings-->
    <string name="okay_caps">OKAY</string>
    <string name="cancel_caps">CANCEL</string>
    <string name="insurance_cancel">Cancel</string>
    <string name="confirm_caps">CONFIRM</string>
    <string name="yes_caps">YES</string>
    <string name="no_caps">NO</string>
    <string name="continue_caps">CONTINUE</string>
    <string name="insurance_continue">Continue</string>
    <string name="give_feedback">Give feedback</string>
    <string name="insurance_back">Back</string>
    <string name="insurance_exit">Exit</string>
    <string name="back_caps">BACK</string>
    <string name="insurance_done">Done</string>
    <string name="insurance_loading_ellipse">Loading...</string>
    <string name="insurance_application_received">Application received!</string>
    <string name="select_product">Select a product</string>
    <string name="what_do_you_want">What do you want to do?</string>
    <string name="what_would_you_like_to_cover">What would you like\nto cover?</string>
    <string name="select_your_cover">Select your cover</string>
    <string name="what_we_cover_title">What we cover</string>
    <string name="edu_important_cover_details_txt">Important cover details</string>
    <string name="funeral">Funeral</string>
    <string name="search_string">Search</string>
    <string name="funeral_desc">Up to R100 000 cover.</string>
    <string name="life">Life</string>
    <string name="life_desc">Ensure your loved ones’ future with up to R3 million in cover.</string>
    <string name="valuables">Vehicle, home &amp; valuables</string>
    <string name="legal_expense">Legal expenses</string>
    <string name="valuables_desc">Short-term comprehensive insurance.</string>
    <string name="legal_expense_desc">Cover for legal expenses.</string>
    <string name="funeral_cover">Funeral cover</string>
    <string name="make_sure_you_family_covered">Make sure that you and your family are covered for life\'s toughest moments.</string>
    <string name="home_owners_cover">Homeowner\'s cover</string>
    <string name="cover_your_home_from_disaster">Insure your home against unexpected expenses.</string>
    <string name="something_gone_wrong_heading">We\’ve experienced a technical error.</string>

    <string name="something_gone_wrong_message">Please wait a few minutes and try again.</string>
    <string name="education">Education</string>
    <string name="terms_condition_accept">Accept</string>
    <string name="terms_condition_placeholder_title">Terms and conditions</string>
    <string name="insurance_pls_read_tnc"><![CDATA[Please read these <b>terms and conditions.</b>]]></string>
    <string name="insurance_see_more">See more</string>
    <string name="insurance_select_your_property">Select your property</string>
    <string name="insurance_your_property">Your property</string>
    <string name="insurance_select_property_for_quote">Which property would you like us to quote you on?</string>
    <string name="sans_serif_regular">sans-serif</string>
    <string name="sans_serif_medium">sans-serif-medium</string>
    <string name="roboto_bold">Roboto-Bold</string>
    <string name="roboto">Roboto</string>
    <string name="sans_serif_light">sans-serif-light</string>
    <string name="sans_serif_thin">sans-serif-thin</string>
    <string name="bond_number">Bond number</string>
    <string name="property_value">Property value</string>
    <string name="registered_bond_amount">Registered bond amount</string>
    <string name="property_address">Property address</string>
    <string name="old_hoc_dialog">If you decide to take up this new homeowner\'s cover, you\'ll need to cancel your old one to avoid paying for two.</string>
    <string name="insurance_about_your_property">About your property</string>
    <string name="your_geyser_details">Your geysers details</string>
    <string name="insurance_which_property_quote_for_hoc">Please give us a few more details so that we can tailor your quote.</string>
    <string name="insurance_wall_costruction">Wall construction</string>
    <string name="select_wall_material">Select wall material</string>
    <string name="select_roof_material">Select roof material</string>
    <string name="insurance_select">Select</string>
    <string name="insurance_select_day">Select day</string>
    <string name="insurance_select_date">Select date</string>
    <string name="insurance_building_material_brick">Type of building material, eg brick.</string>
    <string name="insurance_roof_construction">Roof construction</string>
    <string name="insurance_building_material_tile">Type of building material, eg tile.</string>
    <string name="insurance_no_of_electrical_geysers">Number of electrical geysers</string>
    <string name="insurance_no_of_solar_geysers">Number of solar geysers</string>
    <string name="insurance_enter_no">Enter number</string>
    <string name="insurance_next">Next</string>
    <string name="insurance_m_interested">I\’m interested</string>
    <string name="insurance_wall_construciton">Wall construction</string>
    <string name="insurance_standard_caps">STANDARD</string>
    <string name="insurance_nonstandard_caps">NON-STANDARD</string>
    <string name="insurance_roof_construciton">Roof construction</string>
    <string name="lets_gets_started">Let\'s get started</string>
    <string name="insurance_progress_title">Step %02d of %02d</string>
    <string name="family_banking_progress_title">Step %02d of %02d</string>
    <string name="you_have_applied_successfully">You\'ve successfully applied for a funeral plan </string>
    <string name="thanks_txt">Thank you for applying. We are processing your information and will contact you within 24 hours.</string>
    <string name="instruction_txt">Don’t forget to cancel your existing policy as soon as your new Nedbank Homeowner’s Cover is confirmed.</string>
    <string name="need_help_txt">Need help?</string>
    <string name="call_txt">Call</string>
    <string name="helpline_number_txt">0800 555 111</string>
    <string name="popup_helpline_number_txt">0800 333 111</string>
    <string name="helpline_email_txt"><EMAIL></string>
    <string name="helpline_email_txt_with_dot"><EMAIL>.</string>
    <string name="key_benefits">Key Benefits:</string>
    <string name="important_cover_details">Important cover details</string>
    <string name="cover_amount_title">Cover amount</string>
    <string name="cover_amount_claim_policy_text">Cover amount: %1$s</string>
    <string name="own_cover_amount_upto">Cover of up to R100 000.</string>
    <string name="family_cover_amount_upto">Cover of up to R30 000 for only R90 a month.</string>
    <string name="cover_amount_upto_30k">Cover of up to R30 000 for only R50 a month.</string>
    <string name="cover_amount_upto_10k">Cover of up to R10 000 for only R30 a month.</string>
    <string name="cover_amount_upto_20k">Up to R200 000 for legal expenses.</string>

    <string name="helpline_nedbank_email_txt"><EMAIL></string>
    <string name="helpline_nedbank_email_txt_with_dot"><EMAIL>.</string>
    <string name="went_wrong_complaint">0800 555 111</string>
    <string name="you_are_almost_done">You\’re almost done</string>
    <string name="vvap_you_are_almost_done">You\’re almost done</string>
    <string name="confirm_email_address">Please confirm you’re email address</string>
    <string name="enter_email_id">Please enter your email address below.</string>
    <string name="confirm_email_id">Please confirm or update your email address below.</string>
    <string name="confirm_txt">Confirm</string>
    <string name="enter_email_address">Enter email address</string>
    <string name="insurance_invalid_email">Please enter a valid email address.</string>
    <string name="insurance_invalid_rsaid">Please enter a valid ID number.</string>
    <string name="insurance_loggedin_user_rsaid_cannot_added">The ID number you have entered is your own, you cannot enter your own ID number for this dependant.</string>
    <string name="hint_email_funeral"><EMAIL></string>
    <string name="policy_doc_txt">This is the email address we’ll send all your policy documents to.</string>
    <string name="email_instruction">This is where we\'ll send your insurance policy documents, and all other communication from the Nedbank Insurance team.</string>
    <string name="insurance_you_r_quote">You\'re quote</string>
    <string name="insurance_what_next">What\'s next?</string>
    <string name="you_can_contact_us_for_moreinfo">You can contact us for more information.</string>
    <string name="all_product_tnc_title">Before you continue, please read and accept the following:</string>
    <string name="all_product_tnc_info_bar">This is not an exhaustive list of T&amp;C’s – refer to the View complete T&amp;C’s link to view the comprehensive list.</string>

    <!-- under writing questions-->
    <string name="insurance_under_writing_title">Complete your medical form</string>
    <string name="insurance_under_writing_info_text">Need help? You\'re welcome to contact us on 0800 333 111 if you\'d like us to go through these questions with you.</string>
    <string name="insurance_under_writing_ques_1">Have you ever had any of the following conditions?</string>
    <string name="insurance_under_writing_sub_ques_1">Heart attack, stroke, organ or respiratory failure, angina, cardiomyopathy, heart failure, peripheral vascular disease, diabetes hypertension and any type of paralysis, epilepsy, congenital/birth disorder or any chronic condition that requires ongoing treatment and monitoring.</string>
    <string name="insurance_under_writing_ques_2">Have you ever been diagnosed with or had any symptoms of cancer, growths or tumors?</string>
    <string name="insurance_under_writing_ques_3">Have you ever been diagnosed or are you waiting for test results for tuberculosis, HIV/Aids or Hepatitis B?</string>
    <string name="insurance_under_writing_ques_4">Are you aware of any health issue for which you are planning to consult with, or have been advised to consult with, a medical practitioner in the next six months? This excludes routine medical and dental check-ups.</string>


    <string name="insurance_under_writing_ques_5">What is your height (cm)?</string>
    <string name="insurance_under_writing_ques_6">What is your current weight (kg)?</string>
    <string name="insurance_under_written_ques_7">Have you been vaccinated against Covid-19 with two doses of the Pfizer vaccine, one dose of the Johnson &amp; Johnson vaccine or any other vaccine that the South African regulator has approved?</string>
    <string name="insurance_under_writing_ques_8">What is your vaccination code?</string>
    <string name="insurance_under_writing_ques_9">Are you waiting for a Covid-19 test result?</string>
    <string name="insurance_under_writing_ques_10">Have you ever tested positive for Covid-19?</string>
    <string name="insurance_under_writing_ques_11">Did you test positive in the last month?</string>
    <string name="insurance_under_writing_ques_12">Have you fully recovered and returned to work in full capacity?</string>
    <string name="insurance_under_writing_ques_13">Are you currently taking any prescribed medication for any medical conditions, and has it been for longer than two weeks?</string>
    <string name="insurance_under_writing_ques_14">Do you plan, or have you been advised, to see a doctor in the next six months for symptoms or conditions other than routine medical check-ups?</string>

    <!-- under writing family questions-->
    <string name="insurance_under_writing_family_title">Complete your family member\'s medical form</string>
    <string name="insurance_under_writing_info_family_text">Need help? You\'re welcome to contact us on 0800 333 111 if you\'d like us to go through these questions with you.</string>
    <string name="insurance_under_writing_ques_family_1">Has your family member ever had a heart attack or stroke, or has your family member been diagnosed with angina,
      cardiomyopathy, heart failure or peripheral vascular disease?</string>
    <string name="insurance_under_writing_ques_family_2">Has your family member ever been diagnosed with or has your family member ever had cancer, growth or tumour symptoms?</string>
    <string name="insurance_under_writing_ques_family_3">What is your family member\'s height (cm)?</string>
    <string name="insurance_under_writing_ques_family_4">What is your family member\'s current weight (kg)?</string>
    <string name="insurance_under_writing_ques_family_5">Is your family member, or has your family member ever been, infected with HIV Aids, Hepatitis B or a sexually transmitted disease?</string>
    <string name="insurance_under_writing_family_ques_6">Is your family member waiting for a Covid-19 test result or has your family member had Covid-19 in the past month?</string>
    <string name="insurance_under_written_ques_family_7">Has your family member been vaccinated against Covid-19 with two doses of the Pfizer vaccine, one dose of the Johnson &amp; Johnson vaccine or any other vaccine that the South African regulator has approved?</string>
    <string name="insurance_under_writing_family_ques_8">What is your family member\'s vaccination code?</string>
    <string name="insurance_under_writing_family_ques_9">Is your family member waiting for a Covid-19 test result?</string>
    <string name="insurance_under_writing_family_ques_10">Has your family member ever tested positive for Covid-19?</string>
    <string name="insurance_under_writing_family_ques_11">Did your family member test positive in the last month?</string>
    <string name="insurance_under_writing_family_ques_12">Has your family member fully recovered and returned to work in full capacity?</string>
    <string name="insurance_under_writing_family_ques_13">Is your family member currently taking any prescribed medication for any medical conditions, and has it been for longer than two weeks?</string>
    <string name="insurance_under_writing_family_ques_14">Does your family member plan, or has your family member been advised,
       to see a doctor in the next six months for symptoms or conditions other than routine medical check-ups?</string>

    <string name="insurance_property_details_caps">PROPERTY DETAILS</string>
    <string name="insurance_electrical_geysers">Electrical geysers</string>
    <string name="insurance_solar_geysers">Solar geysers</string>
    <string name="insurance_premium_breakdown_caps">PREMIUM BREAKDOWN</string>
    <string name="insurance_geyser">Geyser</string>
    <string name="insurance_sasria_caps">SASRIA</string>
    <string name="insurance_policy_fee">Policy fee</string>
    <string name="insurance_total_premium">Total Premium</string>
    <string name="ff_insurance_total_premium">Total premium</string>
    <string name="this_premium_added_your_existing_repayment">This premium will be added to your existing bond repayment of (%1s). The new amount to be debited every month will become (%2s).</string>
    <string name="this_hoc_underwritten_by_nedbank">This homedowner\'s Cover (HOC) is underwritten by Nedbank Insurance  Company Ltd, a registered Financial Services Provider (FSP 41104) and licensed insurer (10/10/129/8/2).</string>
    <string name="you_accept_terms_conditions_part_1">I accept the </string>
    <string name="you_accept_terms_conditions_part_2_quote">T&amp;Cs</string>
    <string name="you_accept_terms_conditions_part_2">T&amp;Cs.</string>
    <string name="insurance_accept">Accept</string>
    <string name="insurance_decline">Decline</string>
    <string name="nedbank_hoc_cover_insures">Nedbank Homeowner\'s cover insures you against damage to your property.</string>

    <string name="your_geyser_cover_insures_against_burst">Your geyser cover insures you against bursts, leaks or overflowing geyser components.</string>
    <string name="sasria_insures_you_against_risks">SASRIA insures you against special risks such as: civil commotion, public disorder, strikes, riots and terrorism.</string>
    <string name="monthly_admin_fee">Monthly administration fee.</string>
    <string name="leaving_so_soon">Leaving so soon?</string>
    <string name="leaving_so_soon_tag">Leaving so soon</string>

    <string name="pls_give_ur_feedback">Please take a moment to give us\n your feedback.</string>
    <string name="it_would_help_understand">It would help us a lot to understand\nwhy you did not stick around.</string>

    <string name="went_wrong_txt">Something went wrong on our side</string>
    <string name="vvaps_something_went_wrong">Something went wrong.</string>
    <string name="vvaps_something_went_wrong_helper">Please check your account details and save again.</string>

    <string name="try_again_txt">Please try again or contact the call centre on</string>
    <string name="try_again_later_txt">Please try again later or contact the call centre on</string>
    <string name="unfortunately_txt">Unfortunately, we can\'t find your property information</string>
    <string name="no_verify_account">We could not verify your account.</string>
    <string name="thank_u_feedback">Thank you for your\nfeedback</string>

    <string name="you_get_call_from_nedbank">You\'ll get a call from Nedbank\nInsurance within a few hours.</string>
    <string name="we_appreciate_your_feedback"><![CDATA[Please tell us why you are <b>not taking</b> up this offer.]]></string>

    <string name="please_tell_us_why">Please tell us why you\'re not\ncontinuing with this application?</string>
    <string name="too_expensive">It\'s too expensive</string>
    <string name="not_enough_cover">There isn\'t enough cover</string>
    <string name="need_more_info">I need more information</string>
    <string name="not_understand_process">I didn\'t understand the process</string>
    <string name="other">Other</string>
    <string name="call_me_back">Call me back</string>
    <string name="send_feedback">Send feedback</string>
    <string name="insurance_feedback">Feedback</string>
    <string name="your_benefits_txt">Your benefits</string>
    <string name="for_help_txt">For help, call</string>
    <string name="help_number_policy">0800 333 111</string>
    <string name="tlc_help_number">0861 262 636</string>
    <string name="insurance_get_quote">Get quote</string>
    <string name="insurance_save_quote">Save quote</string>
    <string name="insurance_save_quote_failed_msg">Quote failed to save.</string>
    <string name="insurance_save_quote_title">Save this quote for later?</string>
    <string name="dont_save_btn">DON\'T SAVE</string>
    <string name="insurance_save_txt">SAVE</string>
    <string name="insurance_save_txt_lowercase">Save</string>
    <string name="insurance_view_save_quote_link">View saved quotes</string>
    <string name="insurance_save_quote_instruction">Your quote has been saved and is valid for 30 days.</string>
    <string name="insurance_save_quote_description">By saving, you will not be accepting the quote. You can still accept it within 30 days.</string>

    <string name="build_your_funeral_cover">Build your funeral cover</string>
    <string name="howmuch_cover_will_you_need">How much cover will you need?</string>
    <string name="howmuch_cover_you_need">How much will you need?</string>
    <string name="my_cover_caps">MY COVER</string>
    <string name="select_your_cover_amount">Enter the amount below</string>
    <string name="mcl_select_your_cover_amount">Enter the cover amount you need</string>
    <string name="add_a_dependant"> Add a dependant</string>
    <string name="your_partner"> Your partner</string>
    <string name="min_amount">Min %s</string>
    <string name="nifp_max_amount">Max %s</string>
    <string name="this_is_max_cover_available">This is the maximum cover available to you.</string>

    <string name="setup_ur_funeral_plan">Set up your funeral plan</string>
    <string name="howmuch_cover_would_you_need">Based on the information you have provided, you qualify for %s life cover.</string>
    <string name="select_who_would_have_cover">Please select who you would like to cover.</string>
    <string name="your_cover_caps">YOUR COVER</string>
    <string name="enter_amount_below">Enter the amount below</string>
    <string name="use_your_nedbank_account_for_debit_order">You\’ll get 10% off for setting up the debit order on your Nedbank account.</string>
    <string name="this_will_be_premium_if_no_nedbank">You\’ll get 5% off your premium if you’re paying from another bank account.</string>
    <string name="premium_per_month_get_off">%s per month. Get up to %s off with the following discounts:</string>
    <string name="percent_10">10%</string>

    <string name="my_cover_add_dependant_note">By selecting just my dependants, you are only able to cover your spouse, parents, grandparents
        and domestic workers under this policy.</string>

    <string name="check_ur_funeral_cover">Check your funeral cover</string>

    <string name="insurance_policy_details_caps">POLICY DETAILS</string>
    <string name="insurance_policy_number">POLICY NUMBER: %s</string>
    <string name="insurance_policy_details_overview_caps">OVERVIEW</string>
    <string name="insurance_your_cover">Your cover</string>
    <string name="insurance_others_cover">%s\'s cover</string>
    <string name="insurance_total_cover">Total cover</string>
    <string name="insurance_cover_type">Cover type/s</string>
    <string name="funeral_policy_premium">Funeral policy premium</string>
    <string name="nedbank_discount_5">Nedbank discount 5%</string>
    <string name="insurance_discount">Discount</string>
    <string name="nedbank_discount_5_per">5% Nedbank client discount</string>
    <string name="nedbank_discount_percentage">%d%% Nedbank client discount</string>
    <string name="non_nedbank_discount_percentage">%d%% Nedbank digital discount</string>
    <string name="discount_value">-R50.00</string>
    <string name="no_discount">No discount</string>
    <string name="your_dependants_cover">Your dependants cover</string>

    <string name="insurance_pl_policy_breakdown">POLICY BREAKDOWN</string>
    <string name="insurance_pl_about_your_property">About your property</string>

    <string name="insurance_pl_premium_breakdown">PREMIUM BREAKDOWN</string>
    <string name="insurance_policy_breakdown">Policy breakdown</string>

    <string name="review_your_quote">Review your quote</string>
    <string name="view_quote">View quote</string>
    <string name="setup_your_family">Set up your family funeral plan</string>
    <string name="add_your_partner">Add your partner</string>


    <string name="your_child">Your child</string>
    <string name="insurance_policy_name">Policy name</string>
    <string name="nedbank_insurance_funeral_cover">Nedbank Insurance Funeral Cover</string>
    <string name="insurance_digital_discount">Nedbank digital discount</string>
    <string name="insurance_client_discount">Nedbank client discount</string>
    <string name="off_5_percent_text">5% off</string>

    <string name="deducted_from_total_premium">Deducted from the total premium</string>
    <string name="total_premium">Total Premium</string>
    <string name="nifp_is_underwritten">The MyCover funeral is underwritten by Nedgroup Life Assurance Company Ltd Reg No 1993/001021/06. Authorised financial services provider (FSP40915) and registered credit provider (NCRCP61).</string>
    <string name="discount_txt">You qualify for \na <b>5% discount.</b></string>
    <string name="discount_desc_txt">Simply use your Nedbank account for this debit order and you\'ll pay a lower monthly premium.</string>
    <string name="is_nedbank_txt">I will be using my Nedbank account</string>
    <string name="is_other_account_txt">I will be using another account</string>
    <string name="add_another_bank_txt">New account details</string>
    <string name="enter_bank_detail">Please enter the bank account details for your future debit order payments.</string>
    <string name="select_txt">Select</string>
    <string name="enter_number">Enter number</string>
    <string name="insurance_bank_name">Bank name</string>
    <string name="min_max_height_mcl">Min 50 cm - Max 270 cm</string>
    <string name="min_max_weight_mcl_error5">Your family member\'s weight should be between 20 kg and 500 kg.</string>
    <string name="min_max_height_mcl_error5">Your family member\'s height should be between 50 cm and 270 cm.</string>
    <string name="min_max_height_mcl_error1">Your height should be between 50 cm and 270 cm.</string>
    <string name="min_max_height_mcl_error2">Please enter a valid height.</string>
    <string name="min_max_height_mcl_error3">Please enter height.</string>
    <string name="min_max_weight_mcl">Min 20 kg - Max 500 kg</string>
    <string name="min_max_weight_mcl_error1">Your weight should be between 20 kg and 500 kg.</string>
    <string name="min_max_weight_mcl_error2">Please enter a valid weight.</string>
    <string name="min_max_weight_mcl_error3">Please enter weight.</string>
    <string name="vaccination_code_helper">Eg CW55MABRY6AV</string>
    <string name="vaccination_code_error1">Please enter a valid vaccination code with 12 alphanumeric characters.</string>
    <string name="vaccination_code_error2">Please enter a valid vaccination code.</string>
    <string name="vaccination_code_error3">Please enter a valid vaccination code without spaces.</string>
    <string name="vaccination_code_error4">Please enter vaccination code</string>

    <string name="insurance_common_banks_caps">COMMON BANKS</string>
    <string name="insurance_other_banks_caps">OTHER BANKS</string>
    <string name="insurance_payment_day">Payment day</string>
    <string name="insurance_payment_date">Payment date</string>
    <string name="insurance_monthly_payment_day_note">You can select a monthly payment date 60 days in advance, but not today\'s date.</string>

    <string name="insurance_debit_order_details">Debit order details</string>
    <string name="please_select_bank_account_for_debit">Please select the bank account you\'d like us to debit for your funeral cover.</string>
    <string name="insurance_date_select_instruction_txt">Select the date on which money is normally paid into your account to avoid missed premiums.</string>
    <string name="insurance_day_select_instruction_txt">Select the day on which money is normally paid into your account to avoid missed premiums.</string>
    <string name="insurance_from_which_account">From which account?</string>
    <string name="insurance_select_account">Select account</string>
    <string name="insurance_monthly_payment_date_text">This will be your monthly payment date.</string>
    <string name="insurance_monthly_payment_day_text">This will be your monthly payment day.</string>
    <string name="insurance_account_number_text">*This has to be your current or savings account.</string>
    <string name="insurance_debit_order_info">* This is the date from which your policy will be active.</string>
    <string name="you_have_chosen_month_last_day">You have chosen the last day of the month as your payment date. This means your payment date will change based on the number of days in the month.</string>
    <string name="already_covered_nedbank_dialog">We see that you\'re already covered for %s on Nedbank\'s funeral plans.\n\nThis means that you can get maximum cover of %s on this plan.</string>

    <string name="you_able_to_add_dependant_soon_dialog">You\'ll be able to add a dependant very soon!\n\nFor now, please call our friendly call center staff to assist you with adding funeral cover for your loved ones.</string>
    <string name="you_able_to_add_dependant_soon">You\'ll be able to add a dependant very soon!\n\nFor now, please call our friendly call centre staff to assist you with adding funeral cover for your loved ones:  <b>************</b>.</string>
    <string name="insurance_type_something">Type something</string>
    <string name="insurance_tell_us_more">Tell us more (optional)</string>
    <string name="processing_txt">We\'ll be in touch over the next 24 hours.</string>
    <string name="we_will_send_txt">To add dependants to your policy for a little\n more than what we\'ve quoted you for, please\n contact us by phone or email.</string>
    <string name="email_change_txt">If your email has changed please contact us on</string>
    <string name="or_str">or </string>
    <string name="or_email">or send an email to</string>
    <string name="member_or_email_txt">or sending an email to</string>
    <string name="your_nedbank_insurance_team">Nedbank Insurance\n</string>
    <string name="contact_insurance_email"><EMAIL></string>
    <string name="contact_insurance_email2"><EMAIL> </string>
    <string name="client_services_nedgrouplife_email"><EMAIL></string>
    <string name="you_will_able_txt">Once assessed we\'ll email the outcome of your application to</string>
    <string name="foundation_funeral_success_txt">We will email the outcome of your application to </string>
    <string name="within_business_day">within 2 business days. </string>
    <string name="insurance_premium_amount">%s pm</string>
    <string name="funeral_plan">Funeral Plan</string>

    <string name="unfortunately_no_email_found">Unfortunately we can’t find \nyour email address.</string>
    <string name="please_call">Please call </string>

    <string name="right_email_address_txt">to give us the \nemail address to send your feedback to.\n\nWhen you call us, you\'ll also be able to add\n dependants to your policy for a little bit\n more than what we\'ve quoted you for.</string>

    <string name="insurance_tnc_error">Insurance Terms and conditions error</string>
    <string name="insurance_try_again">Try again</string>
    <string name="insurance_beneficiary_title">Nominate your beneficiary</string>
    <string name="insurance_beneficiary_desc">In the event of your death, we\'ll pay out your funeral cover to your nominated beneficiary.</string>
    <string name="insurance_family_beneficiary_desc">In the event of your death, we\'ll pay out your funeral cover to your nominated beneficiary. Beneficiaries can only be 18 years and older.</string>
    <string name="must_be_18_or_older">*Must be 18 years or older.\n</string>
    <string name="can_not_younger_than_older_than_age">* Cannot be younger than %s and older than %s.</string>
    <string name="beneficiary_full_name">Full names</string>
    <string name="beneficiary_enter_name">Enter full names</string>
    <string name="beneficiary_name_error">Please enter valid full names.</string>
    <string name="beneficiary_south_african_id_number">South African ID number </string>
    <string name="beneficiary_enter_number">Enter ID number</string>
    <string name="enter">Enter </string>
    <string name="beneficiary_id_number">ID number</string>
    <string name="beneficiary_contact_number">Contact number</string>
    <string name="beneficiary_enter_contact_number">Enter number</string>
    <string name="beneficiary_contact_error">Please enter a valid contact number.</string>
    <string name="beneficiary_skip_for_now">Skip for now</string>
    <string name="beneficiary_skip">Skip</string>

    <string name="pls_round_off_cover_amount">Please round off your cover amount to the nearest R1 000 eg: R44 000, R100 000.</string>
    <string name="pls_round_off_cover_amount_mcl">Please round off your cover amount to the nearest R1 000. Eg: R100 000, R250 000</string>
    <string name="cover_amount_cannot_more_than_75000">Your cover amount can not be more than %s.</string>
    <string name="cover_amount_cannot_less_than_5000">Your cover amount can not be less than R5000.</string>
    <string name="cover_amount_cannot_less_than_minimum">Your cover amount can not be less than %s.</string>
    <string name="dependent_cover_amount_cannot_less_than_minimum">The cover amount cannot be less than %s.</string>
    <string name="dependent_cover_amount_cannot_more_than_maximum">The cover amount cannot be more than  %s.</string>
    <string name="dependent_enter_an_amount">Enter an amount between %1$s and %2$s.</string>
    <string name="dependent_pls_enter_an_amount">Please enter an amount.</string>
    <string name="insurance_calculate_premium">Calculate premium</string>
    <string name="insurance_recalculate_premium">Recalculate premium</string>
    <string name="insurance_monthly_premium">Monthly premium</string>
    <string name="insurance_annual_premium">Annual premium</string>
    <string name="insurance_view_quote">View quote</string>
    <string name="you_have_reached_your_cover_limit">You\'ve reached your cover limit</string>
    <string name="already_covered_75000">It looks like you\’re already covered for the maximum R100 000 we can give you on MyCover funeral.\n\nFor more info call 0800 333 111.</string>
    <string name="already_covered_2000000">It looks like you\’re already covered for the maximum R2 000 000 we can give you on Nedbank Insurance Accidental Death Plans.\n\nFor more info call 0800 333 111.</string>
    <string name="already_my_life_covered_3000000">You can only be covered for a maximum of R3 000 000 on MyCover Life.\n\nFor more info call 0800 333 111.</string>
    <string name="you_not_qualified">It looks like you don\'t qualify</string>
    <string name="cover_lower_than_5000">The cover amount we can offer you is lower than the R5 000 needed to get this MyCover funeral.\n\nFor more info call 0800 333 111.</string>
    <string name="cover_lower_than_10000">The cover amount we can offer you is lower than the R10 000 needed to get this MyCover funeral.\n\nFor more info call 0800 333 111.</string>
    <string name="cover_lower_than_30000">The cover amount we can offer you is lower than the R30 000 needed to get this MyCover funeral.\n\nFor more info call 0800 333 111.</string>
    <string name="cover_lower_than_20000">The cover amount we can offer you is lower than the R20 000 needed to get this Nedbank Insurance Accidental Death Plan.\n\nFor more info call 0800 333 111.</string>
    <string name="my_cover_life_lower_than_100000">The cover amount we can offer you is lower than the R100 000 needed.\n\nFor more info call 0800 333 111.</string>
    <string name="cover_lower_than_100000_desc">The cover amount we can offer you is lower than the R100 000 needed.</string>
    <string name="pls_change_your_cover_amount">Please change your cover amount</string>
    <string name="because_you_already_covered_for_another">Because you\’re already covered for %1s on MyCover funeral, your maximum cover for this plan will be %2s.</string>
    <string name="funeral_already_taken_cover">You already have an existing MyCover Funeral Fixed product and only one product is allowed per policy.\n\nFor more info call 0800 333 111.</string>
    <string name="my_cover_life_already_covered_for">Because you\’re already covered for %1s on MyCover Life, your remaining cover amount for this plan will be %2s.</string>
    <string name="my_cover_life_not_qualified">You don\'t qualify for MyCover Life</string>

    <string name="beneficiary_id_error_not_found">Your beneficiary is not listed at the Department of Home Affairs. Please make sure the ID number is correct. </string>
    <string name="pa_beneficiary_id_not_found">This ID number is not listed at Home Affairs. Please double-check your beneficiary details.</string>
    <string name="beneficiary_id_error">Please enter a valid South African ID number with 13 characters.</string>
    <string name="beneficiary_id_error_no_rsa_citizen">Your beneficiary must be a South African citizen.</string>
    <string name="beneficiary_id_error_below_18">Your beneficiary must be 18 years or older.</string>
    <string name="partner_id_error_below_18">Your spouse can not be younger than 18.</string>
    <string name="beneficiary_id_error_between_18_65"> Your beneficiary must be between 18 and 65 \nyears old.</string>
    <string name="beneficiary_id_error_no_self_rsaid">You can\'t nominate yourself as a beneficiary. Please choose someone else.</string>
    <string name="beneficiary_id_error_not_validate_id">We couldn\'t validate this ID, please skip for now and we will help you add a beneficiary later.</string>
    <string name="dependant_id_error_no_self_rsaid">You can\'t add yourself as a dependant. Please choose someone else.</string>
    <string name="dependant_id_error_above_75">You can only add dependants who are younger than 76.</string>
    <string name="dependant_id_error_above_55">You can only add dependants who are younger than 55.</string>
    <string name="your_partner_can_not_55">Your spouse cannot be older than 54.</string>

    <string name="dependant_id_error_dead">According to Home Affairs records, this dependant is already deceased.</string>
    <string name="dependant_id_already_added">You have already added this dependant to your policy, please choose someone else.</string>

    <string name="your_policy_has_lapsed">Your policy has lapsed </string>
    <string name="you_wont_able_submit_track_claim">You won\'t be able to submit or track any claim.</string>
    <string name="you_wont_able_manage_policy_now">You are unable to make any policy updates at present.</string>
    <string name="your_policy_is_pending">Your policy is pending </string>
    <string name="your_policies">Your policies</string>
    <string name="oops">Oops!</string>
    <string name="this_will_be_self_service">This will be a self-service in the future.</string>
    <string name="we_will_let_u_know_decision">We\'ll let you know as soon as a decision has been made.</string>
    <string name="verifying_photo_let_u_know_decision">We\'re verifying your photos. We\'ll let you know as soon as a decision has been made.</string>
    <string name="verifying_photo_info_title">Your claim is pending</string>
    <string name="manual_upload_photo_info_title">Your claim is awaiting manual photo upload</string>
    <string name="manual_upload_pending_policy_title">Your policy is awaiting manual photo upload</string>
    <string name="manual_upload_photo_info_desc">An agent will contact you to help you to do this via email.</string>
    <string name="you_dont_have_funeral_policies"><![CDATA[You don\’t have any funeral policies.<br/><br/>Why don\’t you get a quote by clicking <b>“Get cover”</b>.]]></string>
    <string name="add_cover_txt">Add cover</string>
    <string name="new_claim_txt">New claim</string>
    <string name="no_policy_to_show">No policies to show</string>
    <string name="no_policy_application_to_show">No policies and applications to show</string>
    <string name="no_application_to_show">No saved applications to show</string>
    <string name="delete_application">Delete application</string>
    <string name="delete_application_des">Are you sure you want to delete this application?</string>
    <string name="reference_no">Reference: %1$s</string>
    <string name="expire_in_day">Expires in %1$d day</string>
    <string name="expire_in_days">Expires in %1$d days</string>
    <string name="expire_today">Expires today</string>
    <string name="get_quote_txt">Get quote</string>
    <string name="get_cover_txt">Get cover</string>
    <string name="my_insurance_txt">My insurance</string>
    <string name="no_save_quote_30_days_helper_text">You can save applications for your vehicle, building, house contents and funeral cover for up to 30 days.</string>
    <string name="get_personalised_policy_quote_home_car">Get personalised building, vehicle, funeral, travel and other insurance cover directly from Nedbank.</string>
    <string name="no_saved_application_to_show_subtitle">You can save applications for your vehicle, buildings, house contents and funeral cover for up to 30 days.</string>
    <string name="insurance_cover_instruction">We might just have the\n insurance cover you need.</string>
    <string name="insurance_get_quote_instruction">Why not get yourself a quote?\nYou will be under no obligation.</string>
    <string name="rand_zero">R0</string>
    <string name="why_not_contact_us">For more details call us on</string>
    <string name="val_insurance_cover">Insurance;%1$s;;</string>
    <string name="pls_enter_valid_amount">Please enter an amount.</string>
    <string name="you_are_about_to_cancel">You are about to cancel and have not submitted your claim. Do you want to continue submitting your claim or cancel?</string>
    <string name="are_you_sure_to_remove">Are you sure you want to remove this dependant?</string>
    <string name="you_have_reached_max_depedants_count">You\'ve reached the maximum number of (%d) dependants for this policy. \n\nTo add another dependant, you will need to remove one.</string>
    <string name="dependent_adjust_amount">Because you\'ve adjusted your cover amount, the maximum cover allowed for your dependants has changed. Please review these amounts.</string>
    <string name="nifp_just_my_dependant_under_age_txt">Details entered for this dependant do not meet the minimum requirements. If you still want to add this dependant, we suggest that you take up the \'Myself and my dependants\' option.</string>
    <string name="detail_no_claim">You\’ll get %d%% off your premium if you\’re paying from your Nedbank account.</string>
    <string name="you_are_about_to_cancel_photo">Your photos will not be saved and you will be required to provide us with your photos again.</string>

    <string name="choose_your_premium_caps">CHOOSE YOUR PREMIUM</string>
    <string name="off_5_percent">(5% off)</string>
    <string name="you_have_to_have_atleast">You have to have at least one solar or electrical geyser. </string>
    <string name="you_can_not_have_more_than_10">You can’t have more than 10 geysers.</string>

    <string name="insurance_select_bank_name">Select bank name</string>
    <string name="insurance_branch_code">Branch code</string>
    <string name="insurance_select_branch_code">Select branch code</string>
    <string name="insurance_account_type">Account type</string>
    <string name="insurance_select_account_type">Select account type</string>
    <string name="insurance_account_number">Account number</string>
    <string name="insurance_enter_account_number">Enter account number</string>
    <string name="search_bank_list_insurance">Enter bank name</string>
    <string name="insurance_bank_title">BANK NAMES</string>
    <string name="insurance_branch_name_or_code_title">BRANCH NAMES AND CODES</string>
    <string name="insurance_search_bank">Search for your bank</string>
    <string name="insurance_search_branch">Search for your branch</string>
    <string name="insurance_branch_name_or_code">Enter branch name or branch code</string>
    <string name="insurance_invalid_account_no">Invalid account number.</string>
    <string name="insurance_enter_account_no">Please enter an account number.</string>
    <string name="insurance_back_content">Back to previous screen</string>
    <string name="we_can_only_debit_bank_account">We can only debit a bank account that is in your name.</string>
    <string name="we_can_only_debit_bank_account_funeral_mcl">We can only debit a bank account that is in your name.\n\nSelect the day on which money is normally paid into your account to avoid missed premiums.</string>

    <!-- hoc-->
    <string name="insurance_review_your_quote">Review your quote</string>
    <string name="insurance_about_your_property_caps">ABOUT YOUR PROPERTY</string>
    <string name="insurance_about_your_property_postal_code_helper">Please enter the postal code to your selected property.</string>
    <string name="insurance_about_your_property_postal_code_error">Please enter a postal code with 4 digits.</string>
    <string name="insurance_your_homeowner_caps">YOUR HOMEOWNER\'S COVER</string>
    <string name="insurance_hoc_premium">Homeowner\'s cover premium</string>
    <string name="insurance_sasria">Sasria</string>
    <string name="insurance_homeowner_underwritten">Nedbank Homeowner\'s Cover is underwritten by Nedgroup Insurance Company Ltd Reg No 1993/00121/06. Authorised financial services provider (FSP41104) and registered credit provider.</string>
    <string name="insurance_confirm_email_address">Please confirm or update your email address below.</string>
    <string name="insurance_enter_email_address">Please enter your email address below.</string>
    <string name="insurance_permium_amount_tooltip">Covers you against damage to your property, including geyser and plumbing components.</string>
    <string name="insurance_sasria_tooltip">SASRIA insures you against special risks such as civil commotion, public disorder, strikes, riots and terrorism.</string>
    <string name="insurance_policy_fee_tooltip">This is your monthly policy fee.</string>
    <string name="if_installment_not_there_msg">This premium will be added to your existing bond repayment. Your new payable amount will be due every month.</string>
    <string name="insurance_premium_added_msg">This premium will be added to your existing bond repayment of (%1s). The new amount owed will be (%2s) as per your existing payment method.</string>
    <string name="insurance_premium_no_content_msg">This premium will be added to your existing bond repayment. Your new payable amount will be due every month.</string>
    <string name="insurance_u_r_done">You’re done!</string>
    <string name="insurance_what_to_expect">Here\'s what to expect:</string>
    <string name="insurance_email_id_msg">The outcome of your application will be\nemailed to</string>
    <string name="insurance_cancel_policy_msg">We recommend that you only cancel your\nexisting policy once your Nedbank\nHomeowner\'s Cover is confirmed.</string>
    <string name="insurance_nedbank_insurance">Nedbank Insurance</string>
    <string name="insure_delete_dependent">Delete your dependent</string>
    <string name="insure_edit_dependent">Edit your dependent</string>
    <string name="insure_add_dependent">Add a dependant</string>
    <string name="insure_your_dependent">YOUR DEPENDANTS (%1$d/%2$d)</string>
    <string name="insure_your_dependent_count">(%1$d/%2$d)</string>
    <string name="insure_your_dependent_fixed">YOUR DEPENDANTS</string>
    <string name="insure_your_beneficiary_fixed">YOUR BENEFICIARIES (%1$d/%2$d)</string>
    <string name="insure_your_beneficiary_count">(%1$d/%2$d)</string>
    <string name="add_dependent_top_txt">Please provide us with your dependant\'s details.</string>
    <string name="add_partner_top_txt">Please provide us with your partner\'s details.</string>
    <string name="please_provide_us_with">Please provide us with your <br />child’s details.</string>
    <string name="insurance_yes_txt">Yes, I do</string>
    <string name="insurance_no_txt">No, I don\'t</string>
    <string name="have_dependent_txt">Do you have your dependant\’s ID number?</string>
    <string name="have_partner_txt">Do you have your partner\’s ID number?</string>
    <string name="add_a_dependent_btn">Add a dependant</string>
    <string name="dependent_id_number">ID number</string>
    <string name="sa_dependent_id_number">SA ID number</string>
    <string name="insure_dependent_name">Name</string>
    <string name="insure_enter_name">Enter name</string>
    <string name="insure_surname">Surname</string>
    <string name="insure_enter_surname">Enter surname</string>
    <string name="insure_dependent_category">Category</string>
    <string name="insure_dependent_relation">Relation</string>
    <string name="insure_relationship">Relationship to you</string>
    <string name="insure_select">Select</string>
    <string name="insure_your_dependent_cover">YOUR DEPENDANTS COVER</string>
    <string name="insure_enter_cover_amount">Enter their cover amount</string>
    <string name="insure_dont_have_dependent_info">If you don\'t have your dependent\'s ID number now, you can add them later. \nAlternatively, call us on\n</string>
    <string name="insure_dont_have_partner_info">We need your partner\'s ID number to\ncontinue.\nPlease send us an email to:  </string>
    <string name="insure_child_member_info">Children between 6 months and 18 years old can be covered.</string>

    <string name="insure_to_complete_process">to complete the process.</string>
    <string name="insure_member_complete_process">to submit their ID number and request a different plan.</string>
    <string name="insure_update_btn">Update</string>
    <string name="insure_remove_btn">Remove</string>
    <string name="string_per_month">%s pm</string>
    <string name="child_count_reached">%1$s (limit of %2$d reached)</string>
    <string name="min_max_cover_amount">Min %1$s and max %2$s</string>
    <string name="vvaps_min_max_cover_amount">Min %1$s - Max %2$s</string>
    <string name="you_can_add_children">You can add children between 0 months and 18 years old.</string>

    <string name="pls_enter_sum_insured">Please enter the sum insured.</string>
    <string name="your_sum_insured_above_than_three_lacs">Your sum insured cannot be less than R300 000.</string>

    <string name="insure_ur_dependant_added">Your dependant has been added</string>
    <string name="insure_reached_max_limit">You have successfully added your dependant and have now reached your maximum allowed for this category. You can still add dependants in other categories.</string>
    <string name="insure_tooltip_min_max_text">The amount of cover you can add is based on your remaining limit for this plan as well as the details you\'ve provided for your dependant.</string>
    <string name="insurance_property_type">Property type</string>
    <string name="insurance_property_usage">Property usage</string>
    <string name="insurance_secondary_residence">Secondary residence</string>
    <string name="insurance_secondary_residence_desc">This property is used for your own residential needs in addition to your primary/main residence.</string>
    <string name="insurance_residential_owner">Residential: owner-occupied</string>
    <string name="insurance_residential_owner_desc">This property is your main place of residence.</string>
    <string name="insurance_residential_tenant">Residential: tenant-occupied</string>
    <string name="insurance_residential_tenant_desc">This property is occupied by a tenant.</string>
    <string name="insurance_combined_residential">Combined residential</string>
    <string name="insurance_combined_residential_desc">This property is used for your residential needs as well as your approved business activities.</string>
    <string name="insurance_holiday_home">Holiday home</string>
    <string name="insurance_holiday_home_desc">This property is used for your leisurely weekend and vacation stays.</string>
    <string name="insurance_house">House</string>
    <string name="insurance_house_desc">Your property is a residential building that consists of a ground floor and/or upper storeys.</string>
    <string name="insurance_smallholding">Smallholding</string>
    <string name="insurance_smallholding_desc">Your property is an agricultural holding that is smaller than a farm.</string>
    <string name="insurance_cluster">Cluster</string>
    <string name="insurance_cluster_desc">Your property forms part of individually owned homes that are situated on a shared estate.</string>
    <string name="insurance_geyser_solution">Senseable Geyser Solution</string>
    <string name="insurance_would_like_get_call">Would you like one of our agents to call you for a quote?</string>
    <string name="insurance_manage_ur_geyser_safety">Manage your geyser safely and easily from your smart device.</string>
    <string name="insurance_senseable_geyser_solution_caps">SENSEABLE GEYSER SOLUTION</string>
    <string name="insurance_dot_text">•	</string>
    <string name="insurance_call_for_quote">Call me for a quote</string>
    <string name="insurance_we_will_be_in_touch_details">We\'ll be in touch for more details on this feature.</string>
    <string name="insurance_its_ur_responsibility_to_ensure">It\’s your responsibility to ensure that your property is not under or over insured at any time as this may affect the outcome of your claims.</string>
    <string name="insurance_sum_insured">Sum insured</string>
    <string name="insurance_aligned_property_amount">Please make sure you capture an amount that is aligned to your property replacement value.</string>
    <string name="claim_pending_photo">Your claims with pending photos</string>

    <!-- HOC Claim -->
    <string name="your_insurable_event">Your insurable event</string>
    <string name="pls_fill_following_details_we_process">Please fill in the following details. We\'ll process your claim as quickly as we can.</string>
    <string name="if_you_r_claiming_for_issue">If you are claiming for an issue that has already been fixed, please contact us on</string>
    <string name="insurance_hoc_claim_email"><EMAIL></string>
    <string name="insurance_hoc_claim_helpline">0800 333 111</string>
    <string name="insurance_or_by_email">or by email at </string>

    <string name="insurance_incident_date">Date of incident </string>
    <string name="what_caused_damage">What caused the damage?</string>
    <string name="reporting_within_60days_mandatory">Reporting a claim within 60 days is mandatory and part of the terms and conditions of cover. A claims agent will get back to you for assessment.</string>
    <string name="explain_what_happened">Please explain what happened.</string>
    <string name="what_happened">What happened?</string>
    <string name="police_case_number">Police case number (optional)</string>
    <string name="date_case_logged">Date case was logged (optional)</string>
    <string name="did_water_cause_damage">Did the water cause any additional damage?</string>
    <string name="reporting_a_theft_case">Reporting a theft case within 48 hours is mandatory and part of the terms and conditions of cover. A claims agent will get back to you for the assessment.</string>
    <string name="insurance_yes">Yes</string>
    <string name="insurance_no">No</string>
    <string name="hoc_claim_get_started">Get started</string>
    <string name="insurance_damages_coverd">Damages covered</string>

    <string name="insurance_text_area_count_size">%d/240</string>

    <string name="insurance_contact_details">Contact details</string>
    <string name="insurance_we_have_following_details">Please enter your contact number and email address below.</string>
    <string name="insurance_contact_number">Contact number</string>
    <string name="insurance_email_address">Email address</string>
    <string name="insurance_add_alternative_contact">Add alternative contact</string>
    <string name="insurance_confirm_details">Confirm details</string>
    <string name="insurance_name">Name</string>
    <string name="insurance_surname">Surname</string>
    <string name="insurance_enter">Enter</string>
    <string name="insurance_what_cause_damage">What caused the damage?</string>
    <string name="insurance_pls_provide_additional">Please provide your additional contact details or details of the person who will give us access to your property.</string>
    <string name="insurance_review_hoc_claim_title">Review your claim</string>
    <string name="insurance_primary_contact_details">Contact details</string>
    <string name="insurance_alternative_contact">Alternative contact details</string>
    <string name="insurance_insurable_event">Your insurable event</string>
    <string name="insurance_excess_amount">Excess amount</string>
    <string name="insurance_review_info">You are responsible for the settlement of any applicable first amount payable/excesses required by the appointed service provider. This payment must be made before any repairs or replacements are initiated. Amounts may vary depending on the outcome of your assessment.</string>
    <string name="insurance_name_surname">Name and surname</string>
    <string name="insurance_date_incident">Date of incident</string>
    <string name="insurance_cause_damage">Cause of damage</string>
    <string name="insurance_police_case_no">Police case number</string>
    <string name="insurance_date_case_logged">Date case was logged</string>
    <string name="insurance_what_happened">What happened</string>
    <string name="insurance_payable_amount">First payable amount</string>
    <string name="insurance_pls_enter_contact_no">Please enter contact number.</string>
    <string name="insurance_pls_enter_valid_contact_no">Please enter a valid contact number.</string>
    <string name="insurance_pls_enter_email_address">Please enter email address.</string>
    <string name="minimum_excess_applies_when_claiming"><![CDATA[A minimum <b>%s</b> excess applies when claiming from this cover.]]></string>
    <string name="insurance_whats_this">What\'s this?\n</string>
    <string name="insurance_whats_covered">What\'s covered</string>
    <string name="insurance_whats_this_row1">Your first amount payable before any repairs or replacements can take place.\n</string>
    <string name="insurance_whats_this_row2">Must be paid directly to your service provider.\n</string>
    <string name="insurance_whats_this_row3">May vary based on the outcome of your assessment.</string>
    <string name="insurance_big_dot">•</string>
    <string name="insurance_resultant_damage">Resultant damage</string>
    <string name="insurance_hoc_claim_success_title">We\'ve received your claim</string>
    <string name="insurance_your_reference_hoc_no"><![CDATA[Reference number is <b>%1$s</b>]]></string>
    <string name="insurance_additional_reference_hoc_no"><![CDATA[For additional damages, the reference number is <b>%1$s</b>]]></string>
    <string name="insurance_additional_geyser_damage">One of our agents will confirm as soon as your claim for additional damages is logged.</string>
    <string name="insurance_hoc_claim_description">A service provider will contact you within 24 hours to arrange for an assessment.\n\nOnce reviewed, our insurance experts will send you the outcome of your claim. </string>
    <string name="insurance_hoc_claim_description_with_cas">A service provider will contact you within 24 hours to arrange for an assessment.\n\nOnce reviewed, our insurance experts will send you the outcome of your claim.\n\nWe\'ll need your SAPS case number before we can finalise.</string>
    <string name="insurance_hoc_claim_description_agent_case">One of our agents will be in touch to kickstart the process.\n\nBest wishes!</string>
    <string name="insurance_nedbank_option">Nedbank</string>
    <string name="insurance_non_nedbank_option">Non Nedbank</string>
    <string name="insurance_accept_tnc_option">Accept T&amp;Cs</string>
    <string name="insurance_accept_consent_option">Accept consent</string>
    <string name="insurance_accept_tnc_button">I accept the  T&amp;Cs Button</string>
    <string name="insurance_select_yes">Select yes for call agent</string>
    <string name="insurance_select_no">Select no for call agent</string>
    <string name="accidental_email_phone">Nedbank Insurance\<EMAIL> or 0800 333 111.</string>
    <string name="vvaps_email_phone">For more information call us on\n 0800 333 111 or send an email to\n <EMAIL>.</string>
    <string name="insurance_accident_underwritten">The Nedbank Insurance Accidental Death Plan is underwritten by Nedgroup Life Assurance Company Ltd Reg No 1993/001021/06. Authorised financial services provider (FSP40915) and registered credit provider (NCRCP61).</string>
    <string name="ff_insurance_accident_underwritten">This policy is underwritten by Nedgroup Life Assurance Company Ltd Reg No 1993/001021/06. Authorised financial services provider (FSP40915) and registered credit provider (NCRCP61).</string>
    <string name="save_quote_message">Your quote will be saved for 30 days.\n
     You can find your saved quote on the dashboard under Insurance.</string>
    <string name="come_back_later">Come back later</string>
    <string name="resume">Resume</string>
    <string name="insurance_select_accidental">Select bank name</string>
    <string name="please_select_bank_account_for_debit_accidental">Please select the bank account you\'d like us to debit for your accidental death cover.</string>
    <string name="please_select_bank_account_for_debit_foundation_funeral">Please select the bank account you\'d like us to debit for your funeral cover.</string>

    <string name="pa_add_spouse">Add your spouse</string>
    <string name="pa_add_child">Add your child</string>
    <string name="pa_add_beneficiary">Add your beneficiary</string>
    <string name="pa_spouse_desc_txt">Please give us your spouse\'s details.</string>
    <string name="pa_child_desc_txt">Please give us your child\'s details.</string>
    <string name="pa_beneficiary_desc_txt">Please give us your beneficiary\'s details.</string>
    <string name="pa_have_spouse_id">Do you have your spouse\’s ID number?</string>
    <string name="pa_have_child_id">Do you have your child\’s ID number?</string>
    <string name="pa_have_beneficiary_id">Do you have your beneficiary\’s ID number?</string>
    <string name="pa_dont_have_spouse_info">If you don\'t have your spouse\'s ID number now, go back and switch your plan type. You can always add your spouse later by contacting us on</string>
    <string name="pa_dont_have_child_info">If you don\'t have your child\'s ID number now, go back and switch your plan type. You can always add your child later by contacting us on</string>
    <string name="pa_dont_have_beneficiary_info">If you don\'t have your beneficiary\'s ID number now, you can add them later. Alternatively, call us on </string>
    <string name="pa_id_error_no_self_rsaid">You can\'t add yourself as a %s. Please choose someone else.</string>
    <string name="pa_spouse_name">spouse</string>
    <string name="pa_child_name">child</string>
    <string name="pa_edit_child_name">Edit child</string>
    <string name="pa_delete_child_name">Delete child</string>
    <string name="pa_edit_spouse_name">Edit spouse</string>
    <string name="pa_delete_spouse_name">Delete spouse</string>
    <string name="pa_beneficiary_name">beneficiary</string>
    <string name="pa_member_id_already_added">You have already added this %s to your policy, please choose someone else.</string>
    <string name="pa_max_age_validation">Your %1$s cannot be older than %2$s.</string>
    <string name="pa_min_age_validation">Your %1$s cannot be younger than %2$s.</string>
    <string name="pa_min_age_beneficiary_validation">Your %1$s must be %2$s years or older.</string>
    <string name="pa_id_error_dead">According to Home Affairs records, this %s is already deceased.</string>
    <string name="pa_email_length_error">Please enter an email address with 50 characters or less.</string>
    <string name="pa_help_number">0860 84 83 28</string>

    <!-- Track HOC Claim-->
    <string name="insurance_track_claim">Track a claim</string>
    <string name="insurance_claim_history">Claim history</string>
    <string name="insurance_no_track_claim">You don\'t have any claims to track</string>
    <string name="insurance_no_history_claim">You don\'t have any claims to show</string>
    <string name="insurance_view_list_of_claim">You can view your list of claims in your claim history.</string>
    <string name="insurance_status">Status:</string>
    <string name="insurance_claim_number">Claim number: %s</string>
    <string name="insurance_claim">Claim</string>
    <string name="insurance_claim_details_caps">CLAIM DETAILS</string>
    <string name="insurance_your_insurable_event_caps">YOUR INSURABLE EVENT</string>
    <string name="insurance_your_dependent_track">YOUR DEPENDANT</string>
    <string name="claim_number">Claim number</string>
    <string name="claim_status">Status</string>
    <string name="track_claim">Track claim</string>
    <string name="tracker_txt">TRACKER</string>
    <string name="insurance_register">Registration</string>
    <string name="insurance_success_register">Your claim has been registered.</string>
    <string name="insurance_track_claim_title">Track claim</string>
    <string name="insurance_track_registered">Registered</string>
    <string name="insurance_track_description">Your claim is being registered.</string>
    <string name="insurance_in_progress">In progress</string>
    <string name="insurance_claim_assessment">Claim assessment</string>
    <string name="insurance_payment">Payment</string>
    <string name="insurance_date_txt">Date:</string>
    <string name="insurance_claim_process">Your claim is being assessed and validated.</string>
    <string name="insurance_claim_completed">Your claim has been assessed and validated.</string>
    <string name="insurance_payment_process">Your claim payment is being made.</string>
    <string name="insurance_payment_completed">Your claim payment has been made.</string>
    <string name="insurance_tracker_complete">You can view your claim here for the next 2 days thereafter it will be moved to your claims history.</string>
    <string name="insurance_rejected_text">We\'ll send you the full details by email or post depending on your selected method of communication.</string>
    <string name="insurance_tracker_email_id"><EMAIL></string>
    <string name="motor_tracker_email_id"><EMAIL></string>
    <string name="insurance_vvap_nedbank_contact_email"><EMAIL></string>
    <string name="insurance_dependant_txt">Dependant</string>
    <string name="insurance_dependant_id_number">Dependant ID</string>
    <string name="insurance_date_cause_title">DATE AND CAUSE OF DEATH</string>
    <string name="insurance_death_date">Date of death</string>
    <string name="insurance_death_cause">Cause of death</string>
    <string name="insurance_cover_amount_txt">Cover amount</string>
    <string name="insurance_paid_to">Paid to</string>
    <string name="insurance_future_date_msg">The cover for the selected dependant will only be effective from %s</string>
    <string name="insurance_unable_load_tracker">We were unable to load the tracker.</string>

    <string name="pl_motor_error_dropoff_mes">MyCover Motor (Toggled off screen) - Please contact client</string>
    <string name="pl_my_cover">MyCover</string>
    <string name="pl_select_cover_building_title">Buildings</string>
    <string name="pl_select_cover_motor_title">Motor</string>
    <string name="pl_select_cover_HHC_title">Home Contents</string>
    <string name="pl_select_cover_home_content_title">Home content</string>
    <string name="pl_select_cover_building_desc">Financial protection in case of damage to your property or any of its features and fittings.</string>
    <string name="pl_select_cover_motor_desc">Comprehensive car insurance that keeps you on the road dependent on the type of cover you have selected.</string>
    <string name="pl_select_cover_home_content_desc">This cover insures you against damage, theft and loss of items in your home.</string>
    <string name="pl_building_review_quote_underwritten">This policy is underwritten by Nedgroup Insurance Company Ltd Reg No 1993/00121/06. Authorised financial services provider (FSP41104) and licensed insurer.</string>
    <string name="pl_my_cover_desc">Insure all of your important assets including your home, house contents, vehicle and other valuables.</string>

    <string name="pl_debit_order_description">Nedbank Insurance will debit your preferred account with your monthly premium.</string>
    <string name="pl_debit_order_note_transaction_text">We\'ll first debit you for the number of days you are covered for. After this, we\'ll take the full insurance premium on the 1st of each month.</string>
    <string name="pl_my_cover_done_email">The outcome of your application will be emailed to * within 2 business days.</string>
    <string name="pl_my_cover_consent_title">Before we continue</string>
    <string name="pl_my_cover_consent_subtitle">Do you agree to the following?</string>
    <string name="pl_my_cover_consent_1">I am willing to share my personal information with Nedbank Insurance and understand that the information collected from me or from third parties are processed, transferred and stored in a secure manner.</string>
    <string name="pl_my_cover_consent_2">I understand that Nedbank Insurance may also send my personal information to third parties. This information will help Nedbank Insurance with its credit rating and other risk profiling needs in order to provide a financial service to me.</string>
    <string name="email_length_hint">50 characters maximum</string>

    <string name="pl_my_cover_select_property_add">Add a property</string>
    <string name="pl_my_cover_select_property_subtitle">Which building would you like us to quote you on?</string>
    <string name="pl_my_cover_select_property_empty_title">We don’t have any of your properties listed</string>
    <string name="pl_my_cover_select_property_empty_subtitle">Please add the property you would like us to quote you on.</string>
    <string name="pl_my_cover_select_property_property_value">Property value</string>

    <string name="pl_my_cover_about_property_subtitle">Help us to understand your exact insurance needs.</string>
    <string name="pl_my_cover_about_property_amount_title">Insured amount</string>
    <string name="pl_my_cover_about_property_amount_ques_1">Does your property have a thatched lapa?</string>
    <string name="pl_my_cover_about_property_amount_ques_2">Is it within 4,5 m of the house?</string>
    <string name="pl_my_cover_about_property_amount_ques_3">Is the building especially exposed to storm, flood, subsidence or landslides?</string>
    <string name="pl_my_cover_about_property_amount_note_1">You\'ll need to make sure that your property is not under or overinsured at any time as this may affect the outcome of your claim.</string>
    <string name="pl_my_cover_about_property_amount_helper">Cannot be less than %1s or more than %2s.</string>
    <string name="pl_my_cover_about_property_less_than_three">Your sum insured cannot be less than %1s.</string>
    <string name="pl_my_cover_about_property_less_than_fifteen">Your sum insured cannot be more than %1s.</string>
    <string name="pl_my_cover_review_quote_note">Your total premium has already been discounted.</string>
    <string name="pl_edit_banking_discount">Without 5% Nedbank client discount applied.</string>
    <string name="pl_edit_banking_your_account">This must to be your current or savings account.</string>
    <string name="pl_edit_banking_with_discount">5% Nedbank client discount applied.</string>

    <string name="security_questions">Security questions</string>
    <string name="security_questions_1_label">Is your property unoccupied for any part of the year?</string>
    <string name="security_questions_2_label">Period of unoccupancy</string>
    <string name="less_than_30_days">Less than 30 days</string>
    <string name="more_than_30_days">More than 30 days</string>
    <string name="security_of_property">Security of property</string>

    <string name="pl_my_cover_finalise_quote_title">Finalise your quote</string>
    <string name="pl_my_cover_finalise_included_cover">Included cover</string>
    <string name="pl_my_cover_finalise_how_it_works">How it works?</string>
    <string name="pl_my_cover_finalise_basic_excess_title">Select your basic excess amount</string>
    <string name="pl_my_cover_basic_excess_title">Select your minimum basic excess amount</string>
    <string name="pl_my_cover_finalise_quote_subtitle">Add this additional cover?</string>
    <string name="pl_my_cover_finalise_quote_sasria_header">SASRIA</string>
    <string name="pl_my_cover_finalise_quote_sasria_subtitle">Insures you against damages caused by special risks such as civil or public unrest and acts of terrorism.</string>
    <string name="pl_my_cover_finalise_quote_sasria_note_title">Personal legal liability</string>
    <string name="pl_my_cover_finalise_quote_sasria_note_desc">Insures you against any medical or other expenses that may arise from an insurable event.</string>
    <string name="pl_my_cover_finalise_quote_premium_option_header">YOUR PREMIUM OPTIONS</string>
    <string name="pl_my_cover_finalise_quote_basic_excess">BASIC EXCESS</string>
    <string name="pl_my_cover_finalise_quote_premium_subtitle">This is your premium options for all your selected risks.</string>
    <string name="pl_combo_finalise_quote_premium_subtitle">This is your premium options for your selected section.</string>
    <string name="pl_my_cover_finalise_quote_premium_monthly">Monthly</string>
    <string name="pl_my_cover_finalise_quote_premium_installment">The amount of premium paid monthly for each month.</string>
    <string name="pl_combo_finalise_quote_premium_installment_monthly">The amount paid on a monthly basis.</string>
    <string name="pl_my_cover_finalise_quote_premium_annually">Annually</string>
    <string name="pl_my_cover_finalise_quote_premium_once_payment1">One payment covering one year\'s worth of premiums.</string>
    <string name="pl_my_cover_finalise_quote_premium_once_payment2">By selecting this option, you will pay for 11.5 months.</string>
    <string name="vvaps_finalise_quote_premium_once_payment1">A single discounted amount which covers 12 months.</string>
    <string name="pl_my_cover_finalise_quote_dialog_title">Please note</string>
    <string name="pl_my_cover_finalise_quote_dialog_desc">You will not be covered against special risks such as civil commotion, public disorder, strikes, riots and terrorism.</string>
    <string name="pl_my_cover_finalise_quote_dialog_back">BACK</string>
    <string name="pl_my_cover_finalise_quote_dialog_okay">OKAY</string>
    <string name="pl_my_cover_finalise_quote_basic_excess_desc">This is how much money you will have to pay when you claim.</string>
    <string name="pl_my_cover_finalise_quote_basic_excess_helper">By selecting a higher basic excess amount, your monthly premium will be lower.</string>
    <string name="pl_my_cover_basic_cover_info">By selecting a higher minimum basic excess amount, your monthly premium will be lower.</string>

    <string name="pl_buildings_include_cover_title">Personal legal liability</string>
    <string name="pl_buildings_include_cover_desc1">Amounts you become legally liable to pay due to accidental:</string>
    <string name="pl_buildings_include_cover_desc2">- Death, bodily injury or illness to any person</string>
    <string name="pl_buildings_include_cover_desc3">- Physical loss of or damage to tangible property</string>

    <string name="pl_buildings_how_it_works_header">How it works</string>
    <string name="pl_buildings_how_it_works_title">Basic excess</string>
    <string name="pl_buildings_how_it_works_subtitle">We\'ve made it easy to work out:</string>
    <string name="pl_buildings_how_it_works_description1">The minimum excess amount for this product is (%1s). So for every claim you will have to pay (%2s) or more.</string>
    <string name="pl_buildings_how_it_works_description2">For claims more than R50 000, you will pay 5% of the claim amount, for example:</string>
    <string name="pl_buildings_how_it_works_description3">R60 000 – 5% = R3 000.</string>
    <string name="pl_buildings_how_it_works_description4">For a claim of R60 000, your excess will be R3 000.</string>

    <string name="pl_my_cover_review_quote_buildings">Buildings</string>
    <string name="your_property_address">Your property address</string>
    <string name="property_address_title">Property address</string>
    <string name="pl_insurance_no_results_found">No results found.</string>
    <string name="pl_incomplete_info_txt">Incomplete information</string>

    <string name="property_address_label">"Please type to search for your property. "</string>
    <string name="physical_address">Physical address</string>
    <string name="city">City</string>
    <string name="residential_address_label">Please confirm their residential address.</string>
    <string name="add_manually">Add manually</string>
    <string name="street_address">Street address</string>
    <string name="property_details_label">Please provide us with your property details.</string>
    <string name="confirm_property_details_label">Please confirm your property details.</string>
    <string name="pl_per_annum">\u0020pa</string>
    <string name="security_options_error">Error in getting security Options</string>
    <string name="enter_street_address">Enter street address</string>
    <string name="select_residential_address">Select address</string>
    <string name="select_insurance_product">Select product</string>

    <string-array name="policy_details_tabs">
        <item>Details</item>
        <item>Claims</item>
        <item>Manage policy</item>
    </string-array>

    <string-array name="insurance_list_tabs">
        <item>Policies</item>
        <item>Applications</item>
    </string-array>
    <string-array name="geyser_counts">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <item>10</item>
        <item>10</item>
    </string-array>
    <!-- My Cover Life -->
    <string name="my_cover_contact_us_with_number">For more info call 0800 333 111</string>
    <string name="my_cover_description">We\'ll pay a lump sum to your loved ones when you die.</string>
    <string name="my_cover_title">MyCover Life</string>
    <string name="my_cover_thank_you_title_mlc">Thank you for the interest in our product</string>
    <string name="my_cover_thank_you_title">Thank you for your interest in our product</string>
    <string name="my_cover_thank_your_time_title">Thanks for your interest</string>
    <string name="my_cover_thank_you_individual_desc">Based on the answers provided, you do not qualify for this product, please try again in the future.</string>
    <string name="my_cover_thank_you_family_desc">Based on the answers provided, your family member does not qualify for this product, but we can still help you with a life cover benefit for yourself.</string>
    <string name="my_cover_thank_you_mcl_individual_desc">Based on the answers provided, you do not qualify for this product.</string>
    <string name="my_cover_thank_you_covid_individual_desc">Based on the answers provided, you do not qualify for this product. Please re-apply in one month\'s time.</string>
    <string name="my_cover_thank_you_covid_family_desc">Based on the answers provided, your family member does not qualify for this product. Please re-apply in one months time.\n\nHowever we can still help you with a life cover benefit for yourself.</string>
    <string name="my_cover_thank_you_contact_us">Please try again or contact the call centre on\n0800 333 111.</string>
    <string name="my_cover_thank_you_covid_individual_contact_us">For more info call 0800 333 111.</string>
    <string name="my_cover_thank_you_get_individual_cover">Get individual cover</string>
    <string name="my_cover_consent_title">Do you have your family member\'s consent?</string>
    <string name="my_cover_consent_desc">We advise that you get your family member\'s permission before sharing their personal details with us.\nIf not, you can take our cover for yourself.</string>
    <string name="my_cover_consent_get_individual_cover">Get individual cover</string>
    <string name="my_cover_amount_family">Your family member\’s cover amount</string>
    <string name="insurance_account_holder_discount">Nedbank accountholder discount</string>
    <string name="my_cover_premium_pattern">Your premium pattern</string>
    <string name="insurance_cover_life_underwritten">This policy is underwritten by Nedgroup Insurance Company Ltd Reg No 1993/001021/06. A licensed financial services provider (FSP41104) and licensed and designated insurer.</string>
    <string name="insurance_select_cover_info">We\’ll ask for your family member\’s medical information. Please ensure that you have their permission to share it.</string>
    <string name="insurance_cover_details">The cover details</string>
    <string name="pls_give_us_below">Please give us the below.</string>
    <string name="monthly_income">Monthly income</string>
    <string name="insurance_education">Education</string>
    <string name="insurance_beneficiary">Beneficiary</string>
    <string name="do_you_smoke">Do you smoke?</string>
    <string name="your_family_member_details">Your family member\’s details</string>
    <string name="relationship_to_you">Relationship to you</string>
    <string name="fb_document_type">Document type</string>
    <string name="insurance_full_names">Full names</string>
    <string name="my_cover_this_person_deceased">According to Home Affairs records, this person is already deceased.</string>
    <string name="my_cover_min_age_rsa_id">Your family member cannot be younger than 18.</string>
    <string name="my_cover_max_age_rsa_id">Your family member cannot be older than 64.</string>
    <string name="my_cover_main_member_rsa_id">You can\'t add yourself as a family member. Please choose someone else.</string>
    <string name="my_cover_invalid_rsa_id">Please enter a valid South African ID number with 13 characters.</string>
    <string name="my_cover_beneficiary_desc">In the event of your death, we\'ll pay out your MyCover Life benefit to one or more of your nominated beneficiaries.</string>
    <string name="my_cover_beneficiary_details">Enter your beneficiary details.</string>
    <string name="add_beneficiary_title">Add your beneficiaries</string>
    <string name="add_beneficiary_subtitle">Choose which loved ones will receive your Life cover benefit.</string>
    <string name="add_beneficiary_note">We highly recommend you provide at least one beneficiary. If you skip this step, we\'ll contact you later to collect these details.</string>
    <string name="my_cover_beneficiary_title">Beneficiary</string>
    <string name="my_cover_beneficiary_info_desc">We highly recommend you provide at least one beneficiary. If you skip this step, we\'ll contact you later to collect these details.</string>
    <string name="cover_amount_cannot_more_than">Your cover amount cannot be more than %s.</string>
    <string name="cover_amount_cannot_less_than">Your cover amount cannot be less than %s.</string>
    <string name="you_have_applied_for_my_cover_life">You\'ve applied for \nMyCover Life</string>
    <string name="you_have_made_the_smart_choice">You’ve made the smart choice for your loved ones!</string>
    <string name="what_is_next">What\'s Next?</string>
    <string name="once_assessed">Once assessed we\'ll email the outcome of your application to * \nwithin 2 business days.</string>
    <string name="my_cover_life_once_assessed">We\'ll send your policy documents to\n</string>
    <string name="my_cover_request_sent_to_your_bank">We’ve also sent a request to your banking app. Please approve it within 24 hours to process your application and secure your cover.</string>
    <string name="if_you_would_like_to_use_your_policy">If you\'d like to use your policy benefit as financial security for a loan, please \n contact us.</string>
    <string name="client_services_nedbank_insurance_email"><EMAIL> or \n0800 333 111</string>
    <string name="email_nedbank_insurance"><EMAIL></string>
    <string name="beneficiary_title_small">beneficiary</string>
    <string name="remove_beneficiary">Remove beneficiary?</string>
    <string name="delete_beneficiary_confirmation_msg">Are you sure want to remove this beneficiary?</string>
    <string name="my_cover_debit_description">Nedbank Insurance will debit your preferred account with your monthly premium.</string>
    <string name="home_affairs_not_responding">Home Affairs is not responding now. Please enter your beneficiary details.</string>
    <string name="send_an_email_to">Send an email to</string>
    <string name="nifp_please_select_bank_account_for_debit">Please select the bank account you\'d like us to debit for your funeral cover.</string>
    <string name="nifp_we_can_only_debit_bank_account">Business accounts are not accepted for premium payments. We can only debit a current or savings bank account that is in your name.\n\nSelect the day on which money is normally paid into your account to avoid missed premiums.</string>
    <string name="nifp_policy_type_no_other_banks">Please note\nYour policy type does not allow payments made from other banks.</string>
    <string name="insurance_account_detail_text">*This has to be your current or savings account.</string>
    <string name="mcl_claim_email"><EMAIL></string>
    <string name="pls_enter_beneficiary_name">Please enter your beneficiary\'s name.</string>
    <string name="pls_enter_beneficiary_surname">Please enter your beneficiary\'s surname.</string>
    <string name="suburb_failed_error_msg">The list of suburbs didn\'t load.</string>
    <string name="province_failed_error_msg">The list of provinces didn\'t load.</string>
    <string name="ensure_house_number">Please make sure to include the street number for the property.</string>
    <string name="account_formatter">%1s Account</string>
    <string name="add_a_property">Add a property</string>
    <string name="your">your</string>
    <string name="pl_my_cover_mcl_consent_1">I am willing to share my personal information with Nedbank Insurance, and authorized and appointed representatives of Nedbank Insurance, and understand that the information collected from me or from third parties will be processed, transferred, and stored in a secure manner.</string>
    <string name="pl_my_cover_mcl_consent_2">Providing any incorrect information may lead to my cover being cancelled or
        claims not being paid out. I will answer the medical and lifestyle questions accurately, honestly, and to the best of my knowledge.</string>
    <string name="my_cover_thank_you_solution_title">Thank you for considering our solution</string>
    <string name="my_cover_thank_you_solution_desc1">Our records indicate that you were previously declined due to health-related responses.</string>
    <string name="my_cover_thank_you_solution_desc2">This means you don’t qualify \nfor MyCover Life at the moment.</string>
    <string name="my_cover_thank_you_solution_desc3">Unfortunately you do not qualify for MyCover Life due to your income being below R6 000.</string>
    <string name="my_cover_thank_you_solution_desc4">Please consider our range of funeral solutions available on the Money app and Online Banking.</string>
    <string name="my_cover_thank_you_solution_desc5">Our records indicate that you were previously declined due to health-related responses.</string>
    <string name="my_cover_thank_you_solution_desc6">This means you don’t qualify for MyCover Life at the moment.</string>
    <string name="my_cover_thank_you_solution_desc7">Based on the information provided, you do not qualify for MyCover Life at this time.</string>
    <string name="my_cover_thank_you_solution_desc8">A Nedbank financial planner will be in touch with you to discuss more suitable solutions.</string>
    <string name="my_cover_thank_you_contact_us_info">For more info call 0800 333 111 <NAME_EMAIL>.</string>
    <string name="my_cover_waiting_period_title">Waiting period in effect</string>
    <string name="my_cover_waiting_period_desc">Your previous application was declined due to your income below our R6 000 minimum requirement.</string>
    <string name="my_cover_waiting_period_desc2">But this isn’t the end, you can reapply from\n</string>
    <string name="my_cover_waiting_period_desc3">When you’re ready, ensure that you meet the minimum income requirement of R6000 or more.</string>
    <string name="my_cover_wait_period_contact_us_detail">For more info call 0800 333 111 or email \<EMAIL>.</string>
    <string name="my_cover_thank_you_contact_us_detail">If you’d like to talk through other suitable \nsolutions or for more information, \ncall us on 0800 333 111 or email \<EMAIL>.</string>

    <string name="my_cover_me_dependant">Myself and my dependants</string>
    <string name="my_cover_just_my_dependants">Just my dependants</string>
    <string name="my_cover_myself">Myself</string>
    <string name="my_cover_plan_nifp">Cover plan</string>

    <string name="are_you_sure_change_your_plan">Are you sure you want to change your cover plan?</string>
    <string name="your_information_not_saved">Your information will not be saved if you change your cover plan, and you will be require
     to provide your information again.</string>
    <string name="my_cover_thank_you_gross_income_validation_title">Unfortunately, based on your monthly income, you do not qualify for this product.</string>
    <string name="my_cover_thank_you_gross_income_validation_desc">We recommend that you take a look at our MyCover Funeral offer as an alternative.</string>
    <string name="gross_monthly_income">Gross monthly income</string>
    <string name="mcl_min_required_amount">Min R6000</string>
    <string name="mcl_min_required_error_msg">To qualify for this product, you must earn a minimum gross income of R6 000.</string>
    <string name="hoc_claim_title_already_claimed">It looks like you\'ve already claimed for this</string>
    <string name="insurance_hoc_claim_description_duplicate">The details you\'ve just entered are very similar to your earlier claim.\n\nIf this is not a duplicate and you would like to claim for another insurable event: please try again tomorrow.</string>
    <string name="hoc_claim_title_try_again">Please try again tomorrow</string>
    <string name="insurance_hoc_claim_description_try_again">We\'ve picked up an issue with some of the details you provided and could not process your claim.</string>
    <string name="hoc_claim_title_limit_reached">You\'ve reached your limit for this year</string>
    <string name="insurance_hoc_claim_description_limit_reached">You can request up to 6 pipe repairs over a period of 12 months.\n\nFor more details, kindly refer to your policy schedule.</string>
    <string name="hoc_claim_title_need_other_insurance">Need building insurance?</string>
    <string name="insurance_hoc_claim_description_need_other_insurance">We\'re unable to process your request on the selected policy.\n\nIt seems to be more suited for Nedbank Homeowner\'s Cover.</string>
    <string name="hoc_claim_title_older_date">Try again?</string>
    <string name="insurance_hoc_claim_description_older_date">Your entry cannot be older than your policy start date.</string>
    <string name="hoc_claim_title_policy_suspended">We can\'t help you right now</string>
    <string name="insurance_hoc_claim_description_policy_suspended">We\'re unable to process your claim because your selected policy is currently suspended.</string>
    <string name="hoc_claim_title_cause_not_applicable">Not applicable</string>
    <string name="insurance_hoc_claim_description_not_applicable">The primary cause selected for this claim is not applicable. \n\nPlease select another.</string>

    <!-- Motor-->
    <string name="pl_motor_about_car_more_about_car">More about your vehicle</string>
    <string name="pl_motor_about_car_is_your_car_financed">Is your vehicle financed?</string>
    <string name="pl_motor_about_car_modifications_to_your_car">Have you made any performance-enhancing modifications to your vehicle?</string>
    <string name="pl_motor_about_car_what_do_you_use_your_car_for">What do you use your vehicle for?</string>
    <string name="pl_motor_about_car_text_private">Private</string>
    <string name="pl_new_motor_about_car_social_and_domestic_purposes">Social and domestic purposes, pleasure or travelling between home and permanent place of work.</string>
    <string name="pl_motor_about_car_text_business">Business</string>
    <string name="pl_new_motor_about_car_business_purpose">Social, domestic, professional, business or occupational purposes.</string>
    <string name="pl_new_motor_about_car_business_new_txt">Combination of private, professional or business and occupational use purposes excluding goods for trade.</string>
    <string name="pl_motor_about_car_social_and_domestic_purposes">Social and domestic purposes, pleasure or travelling between home and work.</string>
    <string name="pl_motor_history_title">Your insurance history</string>
    <string name="pl_motor_history_title_building">Tell us about your buildings insurance history</string>
    <string name="pl_motor_history_title_quick_quote_motor">Tell us about your vehicle insurance history</string>
    <string name="pl_motor_history_title_motor">Confirm your vehicle insurance history</string>
    <string name="pl_motor_history_title_home_content">Tell us about your house content insurance history</string>
    <string name="pl_motor_history_question1">Have you had any judgments against you or an insurer cancel your policy or decline to cover you?</string>
    <string name="pl_motor_history_question4">Have you had any judgements or insurance policy cancellations since [dd/mm/yyyy]?</string>
    <string name="pl_motor_history_question5">Have you submitted any claims since [dd/mm/yyyy]?</string>
    <string name="pl_motor_history_question2">Have you had more than 3 claims in the past 12 months?</string>
    <string name="pl_motor_history_question3">Number of vehicle claims in the past 5 years</string>
    <string name="pl_building_history_question3">Number of building claims in the past 5 years?</string>
    <string name="pl_hhc_history_question3">Number of house content claims in the past 5 years?</string>
    <string name="pl_motor_thank_you_desc">You don\'t qualify for our Motor Insurance right now but please try again soon.</string>
    <string name="pl_motor_you_not_qualify_desc">You don\'t qualify for this insurance right now, but please try again soon.</string>

    <string name="pl_motor_car_security_title">Your vehicle security</string>
    <string name="pl_motor_car_security_anti_theft_title">Select alarm, gear lock or immobiliser</string>
    <string name="pl_motor_car_security_anti_theft_device">Does your vehicle have an alarm, immobiliser or gear lock installed?</string>
    <string name="pl_motor_car_security_tracking_device_title">Does your vehicle have a working tracking device?</string>
    <string name="pl_motor_car_security_exclude_damage_title">Do you wish to proceed with vehicle cover EXCLUDING loss or damage relating to theft and hijacking?</string>
    <string name="pl_motor_car_security_tracking_active_inactive">What type of vehicle-tracking device do you have?</string>
    <string name="pl_motor_car_security_tracking_active_title_toyota">Active with early-warning tracking device</string>
    <string name="pl_motor_car_security_tracking_active_without_warning_title">Active without early-warning tracking device</string>
    <string name="pl_motor_car_security_tracking_active_without_warning_title_desc">A tracking device continues to send a signal to the tracking company control room. The client notifies the tracking company that their vehicle has been stolen.</string>
    <string name="pl_motor_car_security_tracking_active_desc_toyota">A tracking device continues to send a signal to the tracking company control room. The tracking company receives a notification that the vehicle has been stolen from the device\'s early-warning system.</string>
    <string name="pl_motor_car_security_tracking_inactive_title">Inactive tracker</string>
    <string name="pl_motor_car_security_tracking_active_title">Active tracker</string>
    <string name="pl_motor_car_security_tracking_inactive_device_title">Inactive tracker</string>
    <string name="pl_motor_car_security_tracking_active_desc">Your device regularly transmits a signal to the tracking company.</string>
    <string name="pl_motor_car_security_tracking_inactive_desc">Your device does not transmit a signal to the tracking company until you activate it.</string>
    <string name="pl_motor_car_security_daytime_parking_title">Daytime parking conditions</string>
    <string name="pl_motor_car_security_night_parking_title">Night-time parking conditions</string>
    <string name="pl_motor_car_security_anti_theft_header">Select alarm, gear lock or immobiliser</string>
    <string name="pl_motor_car_security_daytime_parking_header">Daytime parking conditions</string>
    <string name="pl_motor_car_security_nighttime_parking_header">Nighttime parking conditions</string>
    <string name="pl_motor_car_security_tracker_company_name">Tracker company name</string>
    <string name="pl_motor_car_security_vehicle_tracker_company_name">Vehicle-tracking company name</string>
    <string name="pl_motor_car_security_vehicle_tracker_company_name_helper">The name can include alphanumeric characters, dashes and spaces.</string>
    <string name="pl_motor_car_security_tracker_company_name_invalid">Please enter a vehicle-tracking company name.</string>
    <string name="pl_motor_car_security_tracker_company_name_special_char">Vehicle-tracking company name can\'t include special characters.</string>
    <string name="pl_motor_car_security_tracking_consent">Do we have your consent to forward your contact details to Tracker Connect (Pty) Ltd to start the installation process?</string>
    <string name="pl_motor_car_security_tracking_install">Install a vehicle-tracking device</string>
    <string name="pl_motor_car_security_tracking_install_loss_damage">Install a tracking device for full theft and hijacking cover</string>
    <string name="pl_motor_car_security_tracking_install_desc">By installing a tracking device in your vehicle, you can get R50 or R100 off on your monthly premium, depending on the retail value of your vehicle.</string>
    <string name="pl_motor_car_security_tracking_install_loss_damage_desc">To get cover for theft and hijacking on your vehicle with a value over R500 000, you must install a working tracking device, or you will not be covered.</string>
    <string name="pl_motor_car_security_common_tracking_title">Tracker company name</string>

    <string name="pl_motor_finalise_subtitle">Additional cover, excess and premium options.</string>
    <string name="pl_motor_finalise_additional_cover">ADDITIONAL COVER</string>
    <string name="pl_motor_finalise_additional_cover_desc">This refers to extra cover or benefits that can be included in, or added to, your insurance policy, and added to the premium.</string>
    <string name="pl_motor_finalise_included_cover_excess">Included cover</string>
    <string name="pl_motor_finalise_add_more_cover">Add more cover</string>
    <string name="pl_motor_finalise_additional_cover_title">Additional cover</string>
    <string name="pl_motor_finalise_view_added_cover">You can view your added cover here.</string>
    <string name="pl_motor_finalise_your_extra_cover">Your extra cover</string>
    <string name="pl_motor_finalise_accessory_cover">Accessory cover</string>
    <string name="pl_motor_finalise_your_excess">Your excess</string>
    <string name="pl_motor_finalise_voluntary_excess">Voluntary excess</string>

    <string name="pl_motor_car_hire_title">To Keep moving in case of an accident or theft, you can hire a car for up to 30 days.</string>
    <string name="pl_motor_car_hire_select_an_option">SELECT AN OPTION</string>
    <string name="pl_motor_car_hire_vehicle_group">Vehicle group</string>
    <string name="pl_motor_car_hire_group_A">Group A</string>
    <string name="pl_motor_car_hire_1400_engine_size">1400 engine size.</string>
    <string name="pl_motor_car_hire_1400_engine_size_example">Example: Kia Picanto or similar.</string>
    <string name="pl_motor_car_hire_group_B">Group B</string>
    <string name="pl_motor_car_hire_1600_engine_size">1600
        size.</string>
    <string name="pl_motor_car_hire_1600_engine_size_example">Example: Toyota Corolla Quest or similar.</string>

    <string name="pl_motor_credit_shortfall_text">This product covers vehicles that are covered by an underlying policy at the policy start date and for the duration of the policy term.</string>
    <string name="pl_motor_credit_shortfall_question">Do you have residual or balloon amount?</string>
    <string name="pl_motor_credit_shortfall_info">This will pay for any amount of your car financing that is not covered by your car insurance policy, after your claim.</string>

    <string name="pl_motor_accessory_cover_title">Accessory cover</string>
    <string name="pl_motor_accessory_cover_heading">What are we covering?</string>
    <string name="pl_motor_accessory_cover_info">Insures you against loss or damage to accessories that belong to your vehicle, even when temporarily removed.</string>
    <string name="pl_motor_accessory_cover_info_2"> You can cover all your accessories for the same amount as your insured amount.</string>
    <string name="pl_motor_accessory_cover_total_cover_amount">Your total cover amount</string>
    <string name="pl_motor_accessory_cover_item_type">Item type</string>
    <string name="pl_motor_accessory_cover_item_description">Item description</string>
    <string name="pl_motor_accessory_cover_item_value">Item value</string>

    <string name="pl_motor_add_more_cover_heading">Would you like to add any cover?</string>
    <string name="pl_motor_cover_you_can_add_heading">Cover(s) you can add </string>
    <string name="pl_motor_add_more_cover">COVER</string>
    <string name="pl_motor_add_more_cover_add_more_cover">Add more products</string>
    <string name="pl_motor_add_more_cover_car_hire">Car hire</string>
    <string name="pl_motor_add_more_cover_vehicle_hire">Vehicle hire</string>
    <string name="pl_motor_add_vehicle_hire">Add vehicle hire</string>
    <string name="pl_motor_add_credit_shortfall">Add credit shortfall</string>
    <string name="pl_motor_add_more_cover_credit_shortfall">Credit shortfall</string>
    <string name="pl_motor_add_more_cover_accessory_cover">ACCESSORY COVER</string>
    <string name="pl_motor_add_more_cover_add_cover">Add cover</string>
    <string name="pl_motor_add_more_cover_excess">EXCESS</string>
    <string name="pl_motor_add_more_cover_voluntary_excess">Voluntary excess</string>

    <string name="pl_motor_voluntary_excess_title">Voluntary excess</string>
    <string name="pl_motor_voluntary_heading">Choosing to pay extra excess whenever you claim from your policy will lower your monthly premium.</string>
    <string name="pl_motor_voluntary_select_voluntary_excess_amount">Select your voluntary excess amount</string>
    <string name="pl_motor_voluntary_excess_amount">Voluntary excess amount</string>

    <string name="pl_motor_cover_excess_toolbar_title">Included cover</string>
    <string name="pl_motor_cover_excess_title">Roadside assistance</string>
    <string name="pl_motor_cover_excess_roadside_desc">There are times when you need emergency roadside services. We offer Roadside assistance, which includes breakdown and accident services.</string>

    <string name="pl_motor_registration_number">Registration number</string>
    <string name="pl_motor_enter_valid_reg_no">Please enter a valid registration number.</string>
    <string name="pl_motor_vin_caps">VIN</string>
    <string name="pl_motor_vin_number">VIN number</string>
    <string name="pl_motor_enter_valid_vin_number">Please enter a valid VIN number.</string>
    <string name="pl_motor_licence_code">Licence code</string>
    <string name="pl_motor_enter">Enter</string>
    <string name="pl_motor_insured_amount">Insured amount</string>
    <string name="pl_motor_select">Select</string>
    <string name="pl_motor_car_make">Vehicle make</string>
    <string name="vehicle_make_caps">VEHICLE MAKES</string>
    <string name="pl_motor_car_type">Vehicle type</string>
    <string name="pl_motor_vehicle_body_type">Vehicle body type</string>
    <string name="pl_motor_car_type_value">Vehicle</string>
    <string name="pl_motor_year">Vehicle year</string>
    <string name="pl_motor_colour">Vehicle colour</string>
    <string name="pl_motor_model">Vehicle model</string>
    <string name="vehicle_model_caps">VEHICLE MODELS</string>
    <string name="pl_motor_model_variant">Vehicle model variant</string>
    <string name="pl_motor_car_details_vin_info">VIN number can be found on the vehicle license disk.</string>
    <string name="pl_motor_car_details_registration_info">3–10 alphanumeric characters, no spaces are used.</string>
    <string name="pl_motor_car_details_info">Max %1s.</string>
    <string name="pl_motor_car_details_info_error">Your insured amount cannot be more than %1s.</string>
    <string name="pl_motor_car_details_get">Get vehicle details</string>
    <string name="pl_motor_car_details_thank_you_title">Unfortunately, we were not able to match to the registration/VIN provided.</string>
    <string name="pl_motor_car_details_retail_price_error">Unfortunately, your vehicle does not meet the qualifying criteria.</string>
    <string name="pl_motor_car_details_thank_you_desc">Make sure you have entered your information correctly.</string>
    <string name="pl_motor_driver_details_thank_you_title">Unfortunately, we were not able to get the driver details.</string>
    <string name="pl_motor_license_code_not_allowed_title">Unfortunately, your license type is not allowed for this policy on our digital platform.</string>
    <string name="pl_motor_driver_details_thank_you_desc">Make sure you have entered the information correctly.</string>
    <string name="pl_motor_driver_licence_expired_thank_you_title">Driver\'s license has expired</string>
    <string name="pl_motor_driver_license_expired_thank_you_desc">Renew the driver\'s license to take up this cover.</string>
    <string name="pl_motor_driver_licence_expiring_thank_you_title">Driver\'s license is expiring today</string>
    <string name="pl_motor_car_details_registration_error_space">Please enter a valid registration number without spaces.</string>
    <string name="pl_motor_car_details_registration_error_special_char">Please enter a valid registration number between 3–10 alphanumeric characters.</string>
    <string name="pl_motor_car_security_tracking_special_char">Vehicle-tracking company name can\'t include special characters.</string>
    <string name="pl_motor_car_security_tracking_empty_error">Please enter a vehicle-tracking company name.</string>
    <string name="insurance_error_empty">This field cannot be empty.</string>
    <string name="pl_motor_car_details_vin_error_space">Please enter a valid VIN number without spaces.</string>
    <string name="pl_motor_car_details_vin_error_length">Please enter a valid VIN number with 17 characters.</string>
    <string name="pl_motor_car_details_vin_error_special_char">Please enter a valid VIN number with letters A through Z and numbers 1 through 0.</string>
    <string name="pl_motor_car_details_vin_error_ioq">Please enter a valid VIN number without the letters I, O and Q.</string>
    <string name="pl_motor_car_details_info_text">The insured amount must be for the retail value, which is the average current selling price of the vehicle on the dealers floor.</string>
    <string name="pl_motor_car_details_info_text2">The insured amount is the retail value of your vehicle, which is the current selling price on the dealer’s floor. This can be retrieved from TransUnion or captured manually by the client.</string>
    <string name="pl_motor__quick_quote_car_details_info_text">Please provide this information to fulfil your policy.</string>
    <string name="pl_motor_car_details_duplicate_registration">You have already added this registration number.</string>
    <string name="pl_motor_car_details_duplicate_vin">You have already added this VIN number.</string>

    <string name="pl_motor_review_product_name_title">Product name</string>
    <string name="pl_motor_review_product_name_value">MyCover</string>
    <string name="pl_motor_review_motor_header">MOTOR</string>
    <string name="pl_motor_review_about_motor">About your motor</string>
    <string name="pl_motor_review_policy_breakdown">Policy breakdown</string>
    <string name="pl_motor_review_basic_excess">Basic excess</string>
    <string name="pl_motor_review_basic_excess_tooltip">This includes roadside assist access.</string>
    <string name="pl_motor_review_voluntary_excess_tooltip">Access to assistance in an accident or emergency.</string>
    <string name="pl_motor_review_premium_breakdown">Premium breakdown</string>
    <string name="pl_motor_review_car_hire">Car hire</string>
    <string name="pl_motor_review_car_hire_tooltip">Choose a rental car for up to 30 days.</string>
    <string name="pl_motor_review_credit_shortfall">Credit shortfall insurance</string>
    <string name="pl_motor_review_credit_shortfall_tooltip">Cover for the outstanding amount on your vehicle finance.</string>
    <string name="pl_motor_review_accessory_cover">Accessory cover</string>
    <string name="pl_motor_review_accessory_cover_tooltip">Cover for loss or damage to your car accessories.</string>
    <string name="pl_motor_review_sasria">Sasria</string>
    <string name="pl_motor_review_sasria_tooltip">Insurance against special public risks, such as riots.</string>
    <string name="driver_information">Driver\'s information</string>
    <string name="confirm_driver_information">Confirm driver\'s information</string>
    <string name="driver_question">Who drives the vehicle most of the time?</string>
    <string name="myself">Myself</string>
    <string name="someone_else">Someone else</string>
    <string name="marital_status">Marital status</string>
    <string name="driver_age_range_info">* Must be between 18 and 84 years old.</string>
    <string name="driver_age_range_info_vvaps">* Must be between 18 and 65 years old.</string>
    <string name="confirm_address_question">Is %1s %2s residential address?</string>
    <string name="driver_id_error_no_self_rsaid">You can\'t add yourself. Please choose someone else.</string>
    <string name="postal_code">Postal code</string>
    <string name="postal_code_tooltip">Postal codes are automatically added and can\'t be changed.</string>
    <string name="dha_tooltip_common_txt">Please call 0800 333 111 if any of the information is incorrect.</string>
    <string name="pl_motor_accessory_cover_search_for_item">Search for your item</string>
    <string name="vehicle_make_search_title">Search for your vehicle make</string>
    <string name="vehicle_model_search_title">Search for your vehicle model</string>
    <string name="vehicle_model_variant_search_title">Search for your vehicle model variant</string>
    <string name="pl_motor_accessory_enter_item">Enter item</string>
    <string name="pl_motor_accessory_cover_item">Item</string>
    <string name="pl_motor_south_african_currency_sign">R</string>
    <string name="pl_motor_cover_remove_message">Are you sure you want to remove your cover?</string>
    <string name="pl_motor_accessory_item_info_error">The item value cannot be more than %1s.</string>
    <string name="pl_motor_accessory_item_info_less_error">The item value cannot be less than %1s.</string>
    <string name="no_result_found">No result found.</string>
    <string name="unable_to_find_location_details">Unable to find location details</string>

    <string name="pl_motor_thank_you_interest_title">Thank you for your interest in the My vehicle product</string>
    <string name="pl_motor_thank_you_interest_sub_title">Alternatively, an agent may give you a call.</string>
    <string name="pl_motor_error_screen_contact_us">To take out cover, call us on</string>
    <string name="pl_motor_or_select">or select the</string>
    <string name="pl_motor_call_me_back">Call me back</string>
    <string name="pl_motor_error_screen_choose_option">option and we’ll contact you.</string>

    <string name="pl_motor_receive_call_back_request">We have received your call me back request</string>
    <string name="pl_motor_explain_call_steps">We’ll give you a call to explain the next steps shortly.</string>
    <string name="pl_motor_reg_vin">Registration number/VIN</string>
    <string name="pl_motor_no_reg_vin">No registration number/VIN</string>
    <string name="do_have_reg_vin">Do you have your vehicle registration number or VIN?</string>
    <string name="please_provide_reg_vin">Please provide your registration/VIN number to proceed.</string>
    <string name="please_confirm_reg_vin">Please confirm your registration/VIN number to proceed.</string>

    <!--toyota tracking -->
    <string name="continue_without_theft">Continue without theft and hijacking cover</string>
    <string name="no_continue_theft_active_tracker_highlighted_text">excluding</string>
    <string name="no_continue_theft_active_tracker">You have opted to continue without an Active tracker with early-warning. Do you wish to continue with vehicle cover excluding loss or damage relating to theft and hijacking? </string>
    <string name="no_covered_accidental_loss">This means you will be covered for only accidental loss and damage.</string>
    <string name="no_continue_theft_active_tracker_over_500k">You have opted to proceed with vehicle cover EXCLUDING loss or damage relating to theft and hijacking.</string>
    <string name="no_covered_accidental_loss_over_500k">You will be covered for accidental loss and damage to your vehicle until your tracker has been installed.</string>
    <string name="yes_continue_theft_active_tracker">Do you want to continue taking out cover for your vehicle without cover for theft or hijacking until your tracker has been installed? </string>
    <string name="yes_covered_accidental_loss">You will be covered for accidental loss and damage to your vehicle only.</string>
    <string name="vehicle_over_500k_no_tracker1">You have indicated that you do not have a tracking device, and have chosen not to proceed with vehicle cover excluding loss or damage due to theft and hijacking.</string>
    <string name="vehicle_over_500k_no_tracker2">This means cover cannot be provided at this time.</string>

    <string name="tracker_connect_information">Tracker Connect (Pty) Ltd will be calling you soon to collect more information and arrange the installation of your Active with early-warning tracking device.</string>

    <string name="pl_motor_car_security_early_active_warning_tracking">Active without early-warning tracking device</string>
    <string name="pl_motor_car_security_early_warning_tracking_active_desc">A tracking device continues to send a signal to the tracking company control room. The client notifies the tracking company that their vehicle has been stolen.</string>
    <string name="contacted_for_installation">You will be contacted for installation</string>
    <string name="pl_motor_car_security_active_warning_tracking_install">Install an Active with early-warning tracking device</string>
    <string name="pl_motor_car_security_active_warning_tracking_desc">To get cover for theft and hijacking of your high-risk Toyota, install an Active with early-warning tracking device, or you will not be covered.</string>
    <string name="pl_motor_car_security_active_warning_theft_dec">To get cover for theft or hijacking of your high-risk Toyota, install an Active with early-warning tracking device, or you will not be covered. Get R50 or R100 off on your monthly premium by installing a tracking device based on your vehicle\'s value.</string>
    <string name="pl_motor_car_security_theft_dec_highlighted_text">Get R50 or R100 off on your monthly premium</string>

    <string name="expand_collapse_txt">Expand and collapse</string>
    <string name="expand_view_txt">Additional cover view has been expanded</string>
    <string name="collapse_view_txt">Additional cover view has been collapsed</string>
    <string name="review_expand_view_txt">Review quote details has been expanded</string>
    <string name="review_collapse_view_txt">Review quote details has been collapsed</string>
    <string name="edit_extra_cover">Edit your extra cover</string>
    <string name="delete_extra_cover">Delete your extra cover</string>
    <string name="insure_delete_button">Delete</string>
    <string name="insure_edit_button">Edit</string>
    <string name="my_assets">My Assets</string>
    <string name="my_life">My Life</string>
    <string name="product_screen_first_title">MyCover Funeral</string>

    <string name="male">Male</string>
    <string name="female">Female</string>
    <string name="gender">Gender</string>
    <string name="update">Update</string>
    <string name="enter_hint">Enter</string>
    <string name="must_be_between_months">Must be between 0 months and 18 years old.</string>
    <string name="to_add_a_child_older">To add a child older than 18, take a look at our Build your own cover option.</string>
    <string name="enter_first_name">Please enter dependent name</string>
    <string name="enter_last_name">Please enter dependent last name</string>
    <string name="select_relationship">select dependent relationship</string>
    <string name="enter_dob">Please enter dependent date of birth</string>
    <string name="remove_dependent">Remove Dependent?</string>

    <string name="insurance_property_details">Your property details</string>
    <string name="pl_property_details_subtitle">Please enter your property details.</string>

    <string name="pl_hhc_history_question_thank_you">Based on your answers, you didn\'t qualify.</string>

    <string name="pl_hhc_insured_amount_title">Insured amount</string>
    <string name="pl_hhc_insured_amount_desc">How much would you need to pay for the same or similar items you want to cover?</string>
    <string name="pl_hhc_enter_amount_between_ranges">Min %1s. Max %2s.</string>
    <string name="pl_hhc_insured_amount_info">You\'ll need to ensure that your property is not under or over insured at any time as this may affect the outcome of your claim.</string>
    <string name="pl_hhc_enter_amount_more_than">Enter an amount more than %s.</string>
    <string name="pl_hhc_enter_amount_less_than">Enter an amount less than %s.</string>
    <string name="pl_hhc_learn_more">Learn more</string>
    <string name="pl_hhc_home_inventory_guide">Home inventory guide</string>
    <string name="pl_hhc_home_inventory_details_1">The inventory is broken down into sections of the home and should list all items in your home for the current replacement values.</string>
    <string name="pl_hhc_home_inventory_details_2">The items that are specified under the all risks section may be excluded from this list.</string>
    <string name="pl_hhc_home_inventory_details_3">Need help to calculate the insured amount, see the home inventory guide.</string>
    <string name="pl_hhc_click_here">Click here.</string>

    <string name="hhc_security">Security</string>
    <string name="hhc_security_desc">Tell us about the security of your property.</string>
    <string name="hhc_header_1">DOORS, WINDOWS AND WALLS</string>
    <string name="hhc_header_2">ALARM AND ARMED RESPONSE</string>
    <string name="hhc_header_3">BURGLARIES AND CLAIMS</string>
    <string name="hhc_question_1">Are there security bars on all outside doors?</string>
    <string name="hhc_question_2">Are opening windows secured by bars?</string>
    <string name="hhc_question_3">Is your perimeter wall at least 1.8 meters in height?</string>
    <string name="hhc_picker_lbl">What type of wall is it?</string>
    <string name="hhc_question_4">Do you have a working alarm system?</string>
    <string name="hhc_question_5">Is it connected to armed response?</string>
    <string name="hhc_input_lbl">Name of armed response company</string>
    <string name="hhc_question_6">Have you suffered a burglary at this property?</string>
    <string name="hhc_question_7">Did you claim from your insurance after the burglary?</string>
    <string name="hhc_question_7_more">Tell us more about the most recent claim you have submitted.</string>
    <string name="hhc_date_lbl">Date of most recent claim</string>
    <string name="hhc_currency_lbl">Value of most recent claim</string>
    <string name="hhc_single_space">&#160;</string>

    <string name="hhc_finalise_voluntary_excess_header">VOLUNTARY EXCESS (OPTIONAL)</string>
    <string name="hhc_finalise_voluntary_excess_title">This is an additional amount to the minimum basic excess that may lower your premiums.</string>
    <string name="hhc_finalise_voluntary_excess_add">Add voluntary excess</string>
    <string name="hhc_finalise_premium_title">Select how often you\'d like to pay your premiums:</string>
    <string name="hhc_claim_history_title">Claim history</string>
    <string name="hhc_your_claim_history">Your claim history</string>
    <string name="hhc_claim_history_desc">Tell us when you have submitted claims and the value of those claims.</string>
    <string name="hhc_add_another_claim">Add another claim</string>
    <string name="hhc_add_claim_history">Add claim history</string>
    <string name="hhc_date_of_claim">Date of claim</string>
    <string name="hhc_value_of_claim">Value of claim</string>
    <string name="hhc_header">MY HOUSE CONTENT</string>
    <string name="hhc_review_about_motor">About your home contents</string>
    <string name="hhc_property_address">Property address</string>
    <string name="hhc_flood_risk_from_storm">Flood risk from storm or river</string>
    <string name="hhc_voluntary_excess">Voluntary excess</string>
    <string name="pl_hhc_finalise_dialog_title">Remove Sasria cover?</string>
    <string name="pl_hhc_finalise_dialog_description">You won\'t be covered against damage or loss during civil or public unrest, strikes, riots or acts of terror.</string>
    <string name="pl_hhc_finalise_dialog_remove">REMOVE</string>

    <string name="pl_hhc_voluntary_heading">This is an additional amount to the minimum basic excess that may lower your premiums.</string>
    <string name="pl_hhc_voluntary_remove_cover">Remove cover</string>
    <string name="pl_hhc_select_property_subtitle">Please select your property or add a new property.</string>
    <string name="pl_address_exists_thank_you_desc">To update the details, call us on\n0800 333 111.</string>
    <string name="pl_motor_license_code_contact_desc_part1">You can contact us on 0800 333 111</string>
    <string name="pl_motor_license_code_contact_desc_part2"> to add the vehicle for this license type.</string>
    <string name="pl_address_exists_thank_you_title">We already have this address on our system</string>
    <string name="pl_local_address_exists_thank_you_title">You have already added this property</string>
    <string name="pl_hhc_enter_prop_details_subtitle">Please enter your property details.</string>
    <string name="pl_hhc_basic_excess_tooltip">An amount that you pay yourself when you make an insurance claim.</string>
    <string name="pl_hhc_voluntary_tooltip">An extra amount to your basic excess.</string>

    <string name="age">Age</string>
    <string name="id_number_yes">ID Number:Yes</string>
    <string name="id_number_no">ID Number:No</string>
    <string name="insurance_inventory_title">Inventory</string>
    <string name="required_documents_title">Required documents</string>
    <string name="required_document_title">Required document</string>

    <!--  PL Combo Flow-->
    <string name="combo_education_product_list">Product list</string>
    <string name="combo_build_cover_title">Build your cover</string>
    <string name="combo_all_risk_title">My valuables</string>
    <string name="combo_buildcover_tooltip">My valuables is an extension of your other product. You won\'t be able to take up My valuables as a standalone product. </string>
    <string name="personal_details_email_info">Where we will send your policy documents.</string>
    <string name="premium_title_and_value">Sum insured: %s</string>
    <string name="buildings_title_caps">BUILDINGS</string>
    <string name="my_buildings_title_caps">MY BUILDINGS</string>
    <string name="my_vehicle_title_caps">MY VEHICLE</string>
    <string name="motor_title_caps">MOTOR</string>
    <string name="home_content_title_caps">HOUSE CONTENT</string>
    <string name="all_risk_title_caps">MY VALUABLES</string>
    <string name="combo_house_content">House Content</string>
    <string name="combo_what_product_txt">What product do you want to cover next?</string>
    <string name="combo_personal_details_header">Let\'s confirm your\npersonal details</string>
    <string name="combo_personal_details_header_without_space">Let\'s confirm your personal details</string>
    <string name="pl_combo_basic_excess">Your basic excess</string>
    <string name="pl_combo_basic_excess_desc">Your excess amount will always be 5% of your claim value.</string>
    <string name="pl_combo_basic_excess_new_desc">This is an amount you have to pay when you make a claim.</string>
    <string name="pl_combo_basic_excess_estimated_info">Your quick estimate was based on R7 500 minimum excess, any changes to the basic excess will affect your final premium.</string>
    <string name="pl_combo_basic_excess_work">How basic excess works</string>
    <string name="pl_combo_voluntary_work">How voluntary excess works</string>
    <string name="pl_combo_voluntary_desc">This is an additional amount to the minimum basic excess that may lower your premiums.</string>
    <string name="pl_combo_back_alert">Your information will not be saved if you go back.</string>
    <string name="pl_combo_go_back">Go back</string>
    <string name="pl_combo_how_it_work">How it works</string>
    <string name="pl_combo_example_txt">Example:</string>
    <string name="pl_combo_cumulative_excess">Cumulative excess</string>
    <string name="pl_combo_basic_description_1">The excess is the first amount payable by you after any loss or damage that is covered under this policy.\n\nOur compensation is limited to the amount shown in the schedule, less any excess/s.\n\nThe excess could be a flat excess or a percentage or a combination of them both.</string>
    <string name="pl_combo_voluntary_description_1">This is an additional amount chosen by you that will be added to any other applicable excesses.\n\nThis excess will lower your premium. </string>
    <string name="pl_combo_basic_description_2">Flat excess: Where a flat excess is shown e.g. %1s. If you claim you would only pay %2s.\n\nPercentage excess: Where a percentage excess is shown e.g. it is reflected as 5%% of claim (%3s).\n\nIf you have a claim you would pay %4s or 5%% whichever is the greater.</string>
    <string name="pl_combo_voluntary_description_2">You will pay the basic excess, any applicable cumulative excess as well as the voluntary amount you selected. </string>
    <string name="pl_combo_cumulative_description_1">Your schedule shows when you have to pay one or more excesses per claim.\n\nA cumulative excess will apply when additional amounts to the basic excess, are applicable.\n\nThis would depend on the circumstances of the claim.</string>
    <string name="pl_combo_cumulative_description_2">You will pay the basic excess as well as the cumulative excess as reflected in the schedule.\n\nAn example of where a cumulative excess is applicable is under vehicles.\n\nA basic excess applies, and an additional excess will be applicable for regular driver’s 25 years or younger and/or holding a driving licence less than two years.</string>
    <string name="pl_combo_tooltip_txt">Your schedule shows when you have to pay an excess.</string>
    <string name="pl_combo_tnc_tooltip_txt">This is not an exhaustive list of terms and conditions – refer to the View complete T&amp;Cs link to view the comprehensive list.</string>
    <string name="pl_combo_tnc_title">Before you continue, please read and accept the following:</string>
    <string name="view_complete_t_cs"><![CDATA[View complete T&Cs]]></string>
    <string name="my_cover_my_buildings">MyCover My buildings</string>
    <string name="my_cover_my_vehicle">MyCover My vehicle</string>
    <string name="my_cover_my_house">MyCover My house content</string>
    <string name="my_cover_my_valuables">MyCover My valuables</string>
    <string name="pl_driver_custom_notification_txt">Your driver\'s licence is about to expire. This is a friendly reminder to renew it at your local traffic department.</string>
    <string name="pl_admin_required_bank_statement">One month\'s bank statement or bank \n confirmation letter (stamped by bank).</string>
    <string name="pl_admin_process_message">We will process your request within 24 hours. Look out for a reference email.</string>

    <string name="vehicle_vap_desc">A product range, which covers a host of value added benefits for affordable, reliable value-added cover you can trust.</string>
    <string name="vehicle_vap_title">Vehicle Value \nAdded Product</string>
    <string name="you_qualify_for_the_following_product">You qualify for the following product/s:</string>

    <!-- VVAP-->
    <string name="qualifying_questions">Qualifying questions</string>
    <string name="are_you_a_nedbank">Are you a Nedbank Motor Finance Corporation (MFC) client?</string>
    <string name="is_your_vehicle_financed">Is your vehicle financed?</string>
    <string name="do_you_have_comprehensive_vehicle">Do you have comprehensive vehicle insurance?</string>
    <string name="do_you_have_comprehensive_essential_warranty">Do you have an underlying comprehensive or essential warranty?</string>
    <string name="vvap_qualifying_question3">Is your vehicle currently covered by an underlying Warranty product?</string>
    <string name="vvap_qualifying_question4">Have you made any performance-enhancing modifications to your vehicle?</string>
    <string name="vvap_qualifying_question5">Is your vehicle classified as an exotic vehicle?</string>
    <string name="more_about_car_service_history">Does your vehicle have a full service history?</string>
    <string name="more_about_car_service_history_option1">Yes</string>
    <string name="more_about_car_service_history_option2">None or partial service history</string>
    <string name="more_about_car_service_history_option3">New vehicle, service not required</string>
    <string name="more_about_car_service_history_recommend">We recommend</string>
    <string name="more_about_car_service_history_full_service">Please ensure your vehicle has a full-service history in place.</string>
    <string name="more_about_car_service_history_congratulations">Congratulations on your new vehicle purchase</string>
    <string name="more_about_car_service_history_description1">We glad to know that you’\re taking care of your vehicle. Keep it up!</string>
    <string name="more_about_car_service_history_description1_1">We are glad to know that you’\re taking care of your vehicle. Keep it up!</string>
    <string name="more_about_car_service_history_description2">You visit an accredited service centre to service your vehicle and continue to do so at regular intervals as per manufacturers specifications.</string>
    <string name="more_about_car_service_history_description2_1">You must have the vehicle serviced within 31 days of when your policy starts.</string>
    <string name="more_about_car_service_history_description2_2">This service will be at your own cost, and must be according to the manufacturer\'s specifications.</string>
    <string name="more_about_car_service_history_description3">To keep the vehicle in good working order, remember to service your vehicle at regular intervals as per manufacturers specifications.</string>
    <string name="vvap_qualifying_ques3_tooltip">Covers a variety of incidents, including third-party liability if you are at fault.</string>
    <string name="vvap_qualifying_ques5_tooltip">Modifications and/or alterations to the original vehicle parameter standards.</string>
    <string name="vvap_qualifying_ques6_tooltip">An exotic vehicle is a rare, collectible or high-end sports vehicle.</string>
    <string name="do_you_have_vehicle_warranty">Do you currently have a vehicle warranty?</string>
    <string name="have_you_made_any_performance">Have you made any performance modifications to your vehicle?</string>
    <string name="description_of_modifications">Description of modifications</string>
    <string name="vehicle_value_added_products">Vehicle Value Added Products</string>
    <string name="vehicle_value_added_products_add">Add credit shortfall</string>
    <string name="vehicle_value_added_product_gap_warranty">Add gap warranty</string>
    <string name="vehicle_value_added_product_total_loss">Add total loss</string>
    <string name="vehicle_value_added_product_dent_scratch">Add dent and scratch</string>
    <string name="vehicle_value_added_product_tyre_rim">Add tyre and rim</string>
    <string name="vehicle_value_added_product_essential_warranty">Add essential warranty</string>
    <string name="vehicle_value_added_product_comprehensive_warranty">Add comprehensive warranty</string>
    <string name="vehicle_value_added_products_csf_edit">Edit credit shortfall</string>
    <string name="vehicle_value_added_products_gap_edit">Edit gap warranty</string>
    <string name="vehicle_value_added_products_tlc_edit">Edit total loss</string>
    <string name="vehicle_details">Vehicle details</string>
    <string name="your_vehicle_details">Your vehicle details</string>
    <string name="vehicle_kg">kg</string>
    <string name="gross_vehicle_mass">Gross vehicle mass</string>
    <string name="gross_vehicle_mass_tooltip">Gross vehicle mass reading can be found on the vehicle license disk.</string>
    <string name="vehicle_life">Vehicle life</string>
    <string name="current_mileage">Current mileage</string>
    <string name="date_of_reading_mileage">Date of reading mileage</string>
    <string name="your_vehicle_value_added_products">Your vehicle value added product/s</string>
    <string name="your_vehicle_value_added_products_caps">Your qualifying product</string>
    <string name="title_my_cover_essential_warranty">MyCover Essential Warranty</string>
    <string name="title_my_cover_tyre_rim">MyCover Tyre and Rim</string>
    <string name="about_your_tyre_rim">About your tyre and rim</string>
    <string name="title_my_cover_comprehensive_warranty">MyCover Comprehensive Warranty</string>
    <string name="about_your_essential_warranty">About your essential warranty</string>
    <string name="about_your_comp_warranty">About your comprehensive warranty</string>
    <string name="about_your_vehicle">About your vehicle details</string>
    <string name="about_your_credit_shortfall">About your credit shortfall</string>
    <string name="about_your_gap_warranty">About your gap warranty</string>
    <string name="about_your_total_loss">About your total loss</string>
    <string name="finance_house">Finance house</string>
    <string name="finance_account_number">Finance account number</string>
    <string name="finance_amount">Finance amount</string>
    <string name="finance_agreement_start_date">Finance agreement start date</string>
    <string name="finance_agreement_end_date">Finance agreement end date</string>
    <string name="residual_or_balloon_payment">Residual or balloon payment</string>
    <string name="residual_or_balloon_amount">Residual or balloon amount</string>
    <string name="insurer_name">Insurer name</string>
    <string name="insured_amount">Insured amount</string>
    <string name="comprehensive_insurance_start_date">Comprehensive insurance start date</string>
    <string name="vvap_credit_shortfall_title">Credit shortfall</string>
    <string name="vvap_gap_warranty_title">Gap warranty</string>
    <string name="vvap_total_loss_title">Total loss</string>
    <string name="vvap_dent_scratch_title">Dent and scratch</string>
    <string name="vvap_tyre_rim_title">Tyre and rim</string>
    <string name="vvap_essential_warranty_title">Essential warranty</string>
    <string name="vvap_comprehensive_warranty_title">Comprehensive warranty</string>
    <string name="what_you_qualify_for">What you qualify for</string>
    <string name="vvap_more_about_your_car">More about your vehicle</string>
    <string name="vvap_your_warranty_option">Your warranty option</string>
    <string name="vvap_based_on_mileage_text">Based on the mileage and age of your vehicle you qualify for.</string>
    <string name="vvap_warranty_option">Warranty option</string>
    <string name="vvap_warranty_option_this_warranty_option_cover_text">This warranty option covers your vehicle for:</string>
    <string name="vvap_review_warranty_option_cover_text">This warranty option covers your vehicle for:</string>
    <string name="vvap_complete_list_title">The completed list</string>
    <string name="vvap_complete_list_subtitle">Here is a complete list of what the warranty option covers your vehicle for.</string>
    <string name="vvap_warranty_option_premium">Premium</string>
    <string name="vvap_warranty_option_engine">Engine</string>
    <string name="vvap_warranty_option_gearbox">Gearbox</string>
    <string name="vvap_warranty_option_differential">Differential</string>
    <string name="vvap_warranty_option_differential_lock">Differential lock</string>
    <string name="vvap_warranty_option_transfer">Transaxle</string>
    <string name="vvap_warranty_option_transfer_box">Transfer box</string>
    <string name="vvap_view_complete_list">View complete list</string>
    <string name="vvap_management_system">Management system</string>
    <string name="vvap_turbo_assembly">Turbo assembly</string>
    <string name="vvap_front_wheel_drive_unit">Front-wheel drive unit</string>
    <string name="vvap_casings">Casings</string>
    <string name="vvap_electronic_ignition">Electronic ignition</string>
    <string name="vvap_air_condition">Air conditioner</string>
    <string name="vvap_cooling_system">Cooling system</string>
    <string name="vvap_free_wheel_hubs">Free-wheel hubs</string>
    <string name="vvap_cv_joints">CV joints</string>
    <string name="vvap_propshafts_and_drive_shafts">Propshafts and drive shafts</string>
    <string name="vvap_steering_mechanism">Steering mechanism</string>
    <string name="vvap_breaking_system">Braking system</string>
    <string name="vvap_fuel_system">Fuel system</string>
    <string name="vvap_electrical_components">Electrical components</string>
    <string name="vvap_electrical_winch">Electrical winch</string>
    <string name="vvap_clutch">Clutch</string>
    <string name="vvap_suspension">Suspension</string>
    <string name="vvap_wheel_bearing">Wheel bearings</string>
    <string name="vvap_radiator">Radiator</string>
    <string name="vvap_catalytic_converter">Catalytic converter</string>
    <string name="vvap_drive_pulleys">Drive pulleys</string>
    <string name="vvap_cylinder_head_gasket">Cylinder head gasket</string>
    <string name="vvap_viscous_and_electrical_fans">Viscous and electrical fan</string>
    <string name="vvap_central_locking">Central locking</string>
    <string name="vvap_electric_sunroof_motor_and_convertible_roof_motor">Electric sunroof motor and convertible roof motor</string>
    <string name="vvap_electric_mirrors">Electric mirrors</string>
    <string name="vvap_gps_navigation_system">GPS navigation system</string>
    <string name="vvap_entertainment_system">Entertainment system</string>
    <string name="vvap_phone_system">Phone system</string>
    <string name="vvap_transponder_key">Transponder key</string>
    <string name="vvap_alarms_and_immobiliser">Alarms and immobiliser</string>
    <string name="vvap_warranty_option_overfueling">Overfuelling</string>
    <string name="vvap_warranty_option_overheating">Overheating</string>
    <string name="vvap_warranty_option_cambelt_failure">Cambelt failure</string>
    <string name="vvap_tell_us_more_about_your_car">Tell us more about your vehicle. </string>
    <string name="vvap_current_mileage">Current mileage (km)</string>
    <string name="vvap_date_reading_mileage">Date of reading mileage</string>
    <string name="vvap_error_reading_must_be_between_zero_to_three_lakh">This reading must be between 1 &#8211; 300 000km.</string>
    <string name="vvap_error_mileage_cant_be_more_than_three_lakh">Current mileage cannot be more than 300 000km.</string>
    <string name="vvap_error_pls_enter_valid_mileage">Please enter a valid mileage.</string>
    <string name="vvap_more_about_car_dialog_text">When your mileage exceeds 300 000 kilometers, this policy will expire.</string>
    <string name="vvap_what_you_qualify">What you qualify for</string>
    <string name="vvap_complete_additional_details">Complete the additional details for credit shortfall.</string>
    <string name="vvap_complete_gap_additional_details">Complete the additional details for gap warranty.</string>
    <string name="vvap_finance_details">Finance details</string>
    <string name="vvap_warranty_details">Warranty details</string>
    <string name="vvap_finance_house">Finance house</string>
    <string name="vvap_warranty_provider">Warranty provider</string>
    <string name="vvap_finance_account_no">Finance account number</string>
    <string name="vvap_finance_amount">Finance amount</string>
    <string name="vvap_eg_r_zeros">Eg R0 000 000.</string>
    <string name="vvap_eg_one_zeros">Eg R1 000 000.</string>
    <string name="vvap_finance_agreement_start_date">Finance agreement start date</string>
    <string name="vvap_finance_agreement_tooltip">Finance agreement start date tool tip</string>
    <string name="vvap_gap_effective_start_date">Effective start date</string>
    <string name="vvap_finance_agreement_end_date">Finance agreement end date</string>
    <string name="vvap_gap_effective_end_date">Effective end date</string>
    <string name="vvap_residual_or_balloon_payment">Is there a residual or balloon payment on your vehicle?</string>
    <string name="vvap_gap_cover_monthly">Are you covered monthly or for a specified number of months (such as 12, 24, 32, etc)?</string>
    <string name="vvap_gap_cover_term">Term (specified number of months)</string>
    <string name="vvap_residual_or_balloon_amount">Residual or balloon amount</string>
    <string name="vvap_insurer_details">Insurer details</string>
    <string name="vvap_insurer">Insurer</string>
    <string name="vvap_eg_12367">Eg 1236710008131.</string>
    <string name="vvap_insured_amount">Insured amount</string>
    <string name="vvap_max_r_five_lakh">Max R500 000.</string>
    <string name="vvap_example_r_five_lakh">Eg R500 000.</string>
    <string name="vvap_insurer_effective_start_date">Insurer effective start date</string>
    <string name="vvap_finance_startdate_tooltip">This date cannot be after the credit shortfall section start date.</string>
    <string name="vvap_gap_effective_start_date_tooltip">This date can\'t be after the start date of the gap warranty section start date.</string>
    <string name="vvap_gap_effective_end_date_tooltip">Your gap warranty cover provides additional cover to supplement your existing warranty product until the cover ends.</string>
    <string name="vvap_finance_enddate_tooltip">If your finance agreement has expired, your risk needs cannot be saved.</string>
    <string name="vvap_residual_amount_tooltip">Where an amount of the total value of the car is deferred or postponed to the end of our contract.</string>
    <string name="vvap_error_residual_cant_more_than_insured_amount">Residual cannot be more than the insured amount.</string>
    <string name="vvap_finalise_quote_premium_subtitle">This is your premium options for your selected product.</string>
    <string name="vvap_error_residual_invalid_amount">Please enter a valid residual or balloon amount.</string>
    <string name="vvap_error_insured_invalid_amount">Please enter a valid insured amount.</string>
    <string name="vvap_error_finance_invalid_amount">Please enter a valid finance amount.</string>
    <string name="vvap_error_residual_cant_more_than_insured_amount_value">Residual amount cannot be more than %1s.</string>
    <string name="vvap_error_insured_amount_cant_more_than_five_lakh">Your insured amount cannot be more than R500 000.</string>
    <string name="vvap_error_insured_amount_less_than">Your insured amount cannot be less than %1s.</string>
    <string name="vvap_error_insured_amount_cant_less_than_residual">This amount cannot be less than %s.</string>
    <string name="vvap_min_and_max_amount">Min %s - Max R500 000.</string>
    <string name="vvap_current_mileage_tooltip">This reading indicates the number of miles a vehicle has travelled.</string>
    <string name="vvap_reading_date_tooltip">The Odometer Reading date can only be from the Model Date. Eg. With a Model date of 2018, your Odometer reading can only be from 2018.</string>
    <string name="vvap_all_claims_must_be_reported">All claims must be reported within 31 days. We will then arrange an assessment.</string>

    <string name="vvap_qualifying_question_all">All</string>
    <string name="vvap_your_vehicle_photos">Your vehicle photos</string>
    <string name="vvap_your_vehicle_photos_caps">YOUR VEHICLE PHOTOS</string>
    <string name="vvap_your_vehicle_photos_desc">For your vehicle to be evaluated, we\'ll need clear photos of each side of your vehicle.</string>
    <string name="vvap_your_vehicle_photos_subtitle">Here are things to keep in mind:</string>
    <string name="vvap_your_vehicle_photos_step1">The photos cannot be too dark or too light.</string>
    <string name="vvap_your_vehicle_photos_step2">Your vehicle must be clearly visible.</string>
    <string name="vvap_your_vehicle_photos_step3">No details should be cut off.</string>
    <string name="vvap_your_vehicle_photos_header">VEHICLE PHOTOS %s/8</string>
    <string name="vvap_your_vehicle_photos_header_title">Vehicle photos</string>
    <string name="vvap_your_vehicle_photos_header_reupload">VEHICLE PHOTOS %s/%s</string>
    <string name="vvap_your_vehicle_photos_claim_info">You can always navigate back to upload all your photos again.</string>
    <string name="vvap_vehicle_engine_type">Engine type</string>

    <string name="vvap_front_vehicle">Front of vehicle</string>
    <string name="vvap_front_left_vehicle">Front left of vehicle</string>
    <string name="vvap_front_right_vehicle">Front right of vehicle</string>
    <string name="vvap_left_side_vehicle">Left side of vehicle</string>
    <string name="vvap_right_side_vehicle">Right side of vehicle</string>
    <string name="vvap_back_vehicle">Back of vehicle</string>
    <string name="vvap_back_left_vehicle">Back left of vehicle</string>
    <string name="vvap_back_right_vehicle">Back right of vehicle</string>
    <string name="vvap_no_photo_uploaded">No photo</string>
    <string name="hundred_percentage">100%</string>
    <string name="vvap_photo_size_10mb_error">The photo file size cannot be more than 10MB. Please re-upload the photo.</string>
    <string name="vvap_photo_format_error">We couldn\'t upload your photo due to the format. Please try again.</string>

    <string name="vvap_your_vehicle_tyres_title">Your vehicle tyres</string>
    <string name="vvap_your_vehicle_tyres_upload_photos">Uploaded tyre and rim photos</string>
    <string name="vvap_your_vehicle_upload_photos">Uploaded vehicle photos</string>
    <string name="vvap_your_vehicle_tyres_description">Find the tyre sizes printed on the side of your vehicle’s tyres.</string>
    <string name="vvap_your_vehicle_tyres_info">The width and ratio of your tyres can vary, but the rim size of your tyres must be the same.</string>
    <string name="vvap_your_vehicle_tyres_front_tyre">Front tyre size</string>
    <string name="vvap_your_vehicle_tyres_tyre_hint">000/00/R00</string>
    <string name="vvap_your_vehicle_tyres_front_tyre_helper">E.g 225 (width)/45 (ratio)/R18 (rim size)</string>
    <string name="vvap_your_vehicle_tyres_back_tyre">Back tyre size</string>
    <string name="vvap_your_vehicle_tyres_back_tyre_helper">E.g 225 (width)/45 (ratio)/R18 (rim size)</string>
    <string name="vvap_your_vehicle_tyres_flat">Are your tyres run-flats?</string>
    <string name="vvap_your_vehicle_tyres_error">Invalid input format. Format e.g. 205/45/R17.</string>
    <string name="vvap_your_vehicle_tyres_rim_size_error">The rim size of your vehicle cannot differ.</string>
    <string name="vvap_vehicle_detail_title">Your vehicle details</string>
    <string name="vvap_your_vehicle_tyres_tooltip">With run-flat tyres, you can drive a little longer if your tyres have been punctured.</string>
    <string name="vvap_life_status_thank_you_title">Unfortunately, we do not cover vehicles classified as rebuild/demolished.</string>
    <string name="vvap_life_status_thank_you_desc">Only new or used vehicles may be covered.\n\nYou can try again if you have entered the registration/VIN incorrectly.</string>
    <string name="vvap_retail_price_error_reason_text">Reason:</string>
    <string name="vvap_retail_price_less_than">The value of your vehicle is less than\n%s.</string>
    <string name="vvap_retail_price_more_than">The value of your vehicle is more than\n%s.</string>d

    <!-- Vehicle flow Combo-->
    <string name="pl_vehicle_detail_toolbar_title">Add your vehicle</string>
    <string name="pl_vehicle_detail_title">Provide your vehicle details</string>
    <string name="pl_vehicle_detail_confirm_title">Confirm your vehicle details</string>
    <string name="pl_vehicle_detail_subtitle">Enter the registration number or VIN for your vehicle.</string>
    <string name="pl_combo_vehicle_excess">Your basic and\nvoluntary excess</string>
    <string name="pl_combo_vehicle_excess_without_next_line">Your basic and voluntary excess</string>
    <string name="pl_combo_select_voluntary">Select your voluntary excess amount</string>
    <string name="pl_vehicle_exists_title">This vehicle is already covered</string>
    <string name="pl_vehicle_exists_description1">We noticed that this vehicle is already covered based on the information you provided.</string>
    <string name="pl_vehicle_exists_description2">Select back to add another vehicle.</string>

    <!-- Combo flow -->
    <string name="pl_review_quote_policy_breakdown">Policy breakdown</string>
    <string name="pl_review_quote_premium">Premium</string>
    <string name="pl_review_quote_premium_breakdown">Premium breakdown</string>
    <string name="pl_review_quote_buildings_title">BUILDINGS</string>
    <string name="pl_review_quote_about_property">About your property</string>
    <string name="pl_review_quote_property_value">Property value</string>
    <string name="pl_review_quote_basic_excess">Basic excess</string>
    <string name="pl_review_quote_property_type">Property type</string>
    <string name="pl_review_quote_property_usage">Property usage</string>
    <string name="pl_review_quote_wall_construction">Wall construction</string>
    <string name="pl_review_quote_roof_construction">Roof construction</string>
    <string name="pl_review_quote_flood_risk">Flood risk from storm or river</string>
    <string name="pl_review_quote_buildings_cover">Buildings cover</string>
    <string name="pl_review_quote_sasria">Sasria</string>
    <string name="pl_review_quote_vehicle_title">VEHICLE</string>
    <string name="pl_combo_vehicle_title">Vehicle</string>
    <string name="pl_review_quote_about_vehicle">About your vehicle</string>
    <string name="pl_review_quote_car_hire">Car hire</string>
    <string name="pl_review_quote_credit_shortfall">Credit shortfall insurance</string>
    <string name="pl_review_quote_accessory_cover">Accessory cover</string>
    <string name="pl_review_quote_voluntary_excess">Voluntary excess</string>
    <string name="pl_review_quote_house_content_title">HOUSE CONTENT</string>
    <string name="pl_review_quote_all_risk_title">MY VALUABLES</string>
    <string name="pl_review_quote_legal_expense_title">LEGAL EXPENSES</string>
    <string name="pl_review_quote_my_cover_premium_title">MYCOVER PREMIUM</string>
    <string name="pl_review_quote_my_cover_discount">10% discount</string>
    <string name="pl_review_quote_about_house_content">About your house contents</string>
    <string name="pl_review_quote_property_address">Property address</string>
    <string name="pl_review_quote_basic_excess_for_specified">Basic excess for specified items</string>
    <string name="pl_review_quote_basic_excess_for_unspecified">Basic excess for unspecified items</string>
    <string name="pl_review_quote_insured_amount">Insured amount</string>
    <string name="pl_review_quote_all_risk_items">Your valuable items</string>
    <string name="pl_review_quote_about_all_risk">About your valuables items</string>
    <string name="pl_review_quote_total_insured">Total insured amount</string>
    <string name="pl_review_quote_jewellery">Jewellery 1</string>
    <string name="pl_review_quote_mobile_communication">Mobile communication 1</string>
    <string name="pl_review_quote_electronics">Electronics 1</string>
    <string name="pl_review_quote_equipment">Equipment 1</string>
    <string name="pl_property_detail_flood_risk_ques">Is your property especially exposed to storm, flood, subsidence or landslides?</string>
    <string name="pl_extra_cover_title">Your extra and included cover</string>
    <string name="pl_extra_cover_subtitle">EXTRA COVER</string>
    <string name="pl_extra_cover_saria_subtitle">Insures you against damages caused by special risks such as civil or public unrest and acts of terrorism.</string>
    <string name="pl_extra_cover_saria_cover_question">Do you want to add Sasria cover?</string>
    <string name="pl_extra_cover_included_cover">INCLUDED COVER </string>
    <string name="pl_extra_cover_personal_legal_liability">Personal legal liability</string>
    <string name="pl_extra_cover_personal_legal_liability_title">Amounts you become legally liable to pay due to accidental:</string>
    <string name="pl_extra_cover_personal_legal_liability_point1">Death, bodily injury or illness to any person</string>
    <string name="pl_extra_cover_personal_legal_liability_point2">Physical loss of or damage to tangible property</string>
    <string name="pl_extra_cover_personal_legal_liability_info">Personal legal liability is included in this policy.</string>
    <string name="your_premium_options">Your premium options</string>
    <string name="your_premium_discounts">Your premium and discounts</string>
    <string name="combo_about_property_screen_desc">Help us to understand your exact insurance needs.</string>
    <string name="pl_combo_property_address_info">This section does not cover a private residence that forms part of a Sectional Title. Sectional title simply describes the separate ownership of a unit within a complex or development.</string>
    <string name="pls_ensure_house_no_inclusion">Please make sure to include the street number for the property.</string>
    <string name="address">Address</string>
    <string name="residential_address">Residential Address</string>
    <string name="add_items">Add items</string>
    <string name="pl_extra_included_cover_title">Your extra and included cover/s</string>
    <string name="pl_debit_order_policy_start_date">Policy start date</string>
    <string name="pl_extra_included_cover_details">This is extra cover or benefits that may be included in, or added to, your insurance policy, and added to the premium.</string>
    <string name="pl_insures_you_against_damages">Insures you against special risks such as civil or public unrest and acts of terrorism at an estimated cost of R2.02 a month or R20.18 a year.</string>
    <string name="pl_accessory_cover">Accessory cover</string>
    <string name="pl_roadside_assistance_title">Roadside assistance</string>
    <string name="pl_accidental_loss_title">Accidental loss and damage</string>
    <string name="pl_we_offer_roadside_assistance">There are times when you need emergency roadside services. \n\nWe offer roadside assistance, which includes breakdown and accident services.</string>
    <string name="pl_accidental_loss_demage_txt">You will have cover for accidental loss and damage,excluding theft and hijacking.</string>
    <string name="pl_review_sasria_tooltip">Insures you against special risks such as civil commotion, public unrest and acts of terrorism.</string>
    <string name="pl_review_building_cover_tooltip">Your monthly fee for this insurance policy.</string>

    <string name="pl_combo_finalise_subtitle">You qualify for a 10% discount because more than 1 type of section was selected.</string>
    <string name="pl_combo_new_discount_subtitle">Because you have chosen more than one product, you could qualify for a discount of up to 10%.</string>
    <string name="pl_combo_new_no_discount_subtitle">These are your premium options for your selected product.</string>
    <string name="pl_combo_premium_pay_title">How do you want to pay your premium?</string>
    <string name="pl_combo_monthly_premium">Your monthly premium</string>
    <string name="pl_combo_annual_premium">Your annual premium</string>
    <string name="pl_combo_discount_premium">You can get up to %s off on your premium with the following discounts:</string>
    <string name="pl_combo_choose_discount_premium">Choose which of the following discounts apply to you:</string>
    <string name="pl_combo_discount_instruction">You\'ll get up to %s off if:</string>
    <string name="pl_combo_discounts_instruction">You\'ll get up to 10% off if:</string>
    <string name="pl_combo_discount_bullet_text_1">you set up a debit order on your Nedbank account to pay your premium.</string>
    <string name="pl_combo_discount_text">you set up a debit order on your Nedbank account to pay your premium.</string>
    <string name="pl_combo_discount_bullet_non_staff_text_1">you set up a debit order on your Nedbank account to pay your premium; and</string>
    <string name="pl_combo_discount_bullet_text_2">you are a Nedbank employee; and</string>
    <string name="pl_combo_discount_bullet_2">you are a Nedbank employee</string>
    <string name="pl_combo_discount_bullet_text_3">you use a digital platform to apply.</string>
    <string name="pl_combo_discount_bullet_text_4">your premium is debited from an account with another bank.</string>
    <string name="pl_combo_discount_bullet_text_5">you use a digital platform to apply.</string>
    <string name="pl_combo_bullet">\u2022</string>
    <string name="pl_combo_other_bank_instruction">You\'ll get up to 5% off if:</string>
    <string name="pl_combo_build_cover_discount_txt">Add two or more different products to receive a discount of up to 10% on your premium.</string>

    <!--ALL RISKS-->
    <string name="all_risk_specified_items_toolbar">My valuables</string>
    <string name="all_risk_specified_items_title">Specified items</string>
    <string name="all_risk_specified_items_subtitle">This includes jewellery, bicycles, electronics, mobile communication items and sports equipment.</string>
    <string name="all_risk_specified_items_info">You can add items up to a max of %s</string>
    <string name="all_risk_specified_items_max_limit">Max %s incl. VAT.</string>
    <string name="all_risk_specified_items_select_item">Select your item</string>
    <string name="all_risk_specified_items_bicycle_description">Description of bicycle</string>
    <string name="all_risk_specified_items_bicycle_value">Value of bicycle</string>
    <string name="all_risk_specified_items_serial_number">Serial number</string>
    <string name="all_risk_specified_items_jewellery_type">Jewellery type</string>
    <string name="all_risk_specified_items_metal_type">Precious-metal type</string>
    <string name="all_risk_specified_items_stone_type">Precious stone type</string>
    <string name="all_risk_specified_mobile_communication">Mobile communication type</string>
    <string name="all_risk_specified_communication_cellphone">Cellphone</string>
    <string name="all_risk_specified_communication_tablet">Tablet</string>
    <string name="all_risk_specified_jewellery_description">Jewellery description</string>
    <string name="all_risk_specified_mobile_description">Description of mobile device</string>
    <string name="all_risk_specified_electronic_description">Description of electronic item</string>
    <string name="all_risk_specified_sports_description">Description of sports equipment</string>
    <string name="all_risk_specified_jewellery_value">Value of jewellery</string>
    <string name="all_risk_specified_electronic_value">Value of electronic item</string>
    <string name="all_risk_specified_mobile_value">Value of mobile device</string>
    <string name="all_risk_specified_sports_value">Value of sports equipment</string>
    <string name="all_risk_specified_mobile_imei">IMEI number</string>
    <string name="all_risk_unspecified_items">Unspecified items</string>
    <string name="all_risk_unspecified_cover_label">Cover amount</string>
    <string name="all_risk_specified_duplicate_title">You have already added this item</string>
    <string name="all_risk_specified_serial_tooltip">Your serial number refers to the identification number printed on the manufactured item.</string>
    <string name="all_risk_specified_learn_more_title">What is an IMEI?</string>
    <string name="all_risk_specified_learn_more_desc1">IMEI stands for international mobile equipment identity.</string>
    <string name="all_risk_specified_learn_more_desc2">Think of it as your phone\'s fingerprint — it\'s a 15-digit number unique to each device.</string>
    <string name="all_risk_added_valuables_title">Which items would you like to add?</string>
    <string name="all_risk_added_valuables_subtitle">You have a maximum of %s in value for your added items.</string>
    <string name="all_risk_added_valuables_specified">My specified items</string>
    <string name="all_risk_added_valuables_unspecified">My unspecified items</string>
    <string name="all_risk_added_valuables_specified_desc">This includes jewellery, bicycles, electronics, mobile communication items and sports equipment.</string>
    <string name="all_risk_added_valuables_unspecified_desc">This includes handbags, sunglasses, watches and other loose items. These items are covered by one cover amount.</string>
    <string name="all_risk_added_valuables_added_title">Your insured items</string>
    <string name="all_risk_added_valuables_insured_total">Total insured amount</string>
    <string name="all_risk_added_valuables_add_items">Add items</string>
    <string name="all_risk_added_valuables_added_subtitle">You can add items up to a max of %s.</string>
    <string name="all_risk_added_valuables_value_remaining">You have %s remaining.</string>
    <string name="all_risk_basic_excess_specified_header">BASIC EXCESS FOR SPECIFIED ITEMS</string>
    <string name="all_risk_basic_excess_unspecified_header">BASIC EXCESS FOR UNSPECIFIED ITEMS</string>
    <string name="all_risk_extra_cover_title">Your included cover</string>
    <string name="all_risk_extra_cover_question">Insures you against damages caused by special risks such as civil or public unrest and acts of terrorism.</string>
    <string name="all_risk_valuable_dialog_title">For your items valued above R23 000.00</string>
    <string name="all_risk_valuable_dialog_desc">Remember to email your valuation certificates with your policy <NAME_EMAIL>.</string>
    <string name="pl_combo_insure_delete_button">Delete your cover</string>
    <string name="pl_combo_insure_edit_button">Edit your cover</string>
    <string name="all_risk_specified_unspecified_title">Specified &amp; unspecified items</string>

    <string name="pl_combo_debit_order_description_yearly">Nedbank Insurance will debit your preferred account with your annual premium.</string>
    <string name="pl_combo_debit_order_note_yearly_transaction_text">Annual premium payment will be the date that the policy is issued. E.g. If it is issued on 17th March 2021, then payment due date will be 17th March 2021.</string>
    <string name="pl_help_number">0861 262 636</string>
    <string name="pl_claim_email"><EMAIL></string>
    <string name="pls_ensure_street_address">Please ensure inclusion of a number for the house of street.</string>
    <string name="pl_debt_bank_title">Nedbank Insurance will debit your preferred bank account.</string>

    <string name="rdr_desc_family">To benefit from a reduced waiting period on your new funeral policy, please confirm if you or any dependant has cancelled a previous funeral policy in the last 31 days, and that you can provide us with proof of cancellation.</string>
    <string name="rdr_desc_individual">To benefit from a reduced waiting period on your new funeral policy, please confirm if you have cancelled a previous funeral policy in the last 31 days, and that you can provide us with proof of cancellation.</string>
    vvap_licence_type_required_tooltip
    <!--Vehicle VVAP -->
    <string name="vvap_vehicle_mass">Gross vehicle mass</string>
    <string name="vvap_vehicle_life_status">Vehicle life status</string>
    <string name="vvap_vehicle_details_title">YOUR VEHICLE DETAILS</string>
    <string name="vvap_vehicle_details_mass_tooltip">Gross vehicle mass reading can be found on the vehicle license disk.</string>
    <string name="vvap_licence_type_required_tooltip">The licence type required for your vehicle.</string>
    <string name="vvap_insurable_electrical_tooltip">Failure of the electrical components listed under Benefits, for example GPS navigation systems.</string>
    <string name="vvap_insurable_mechanical_tooltip">Failure of the mechanical components listed under Benefits, for example the engine and gearbox.</string>
    <string name="vvap_vehicle_body_type">Vehicle body type</string>
    <string name="vvap_variant_model">Vehicle model variant</string>
    <string name="vvap_variant_model_caps">VEHICLE MODEL VARIANTS</string>
    <string name="vvap_petrol">Petrol</string>
    <string name="vvap_diesel">Diesel</string>
    <string name="vvap_max_3500_kg">Min 750kg - Max 3500kg.</string>
    <string name="vvap_engine_size_between_600_to_30000cc">Min 600cc - Max 3000cc</string>
    <string name="pl_engine_size_between_600_to_30000cc">Engine size should be between 600cc and 3000cc.</string>
    <string name="vvap_engine_size_tooltip">Engine size can be found in your owner\'s manual - it should be listed under \'Specifications\' or \'Mechanical Information\'.</string>
    <string name="vvap_gross_vehicle_mass_tooltip">Gross vehicle mass reading can be found on the vehicle license disk.</string>
    <string name="vvap_new">New</string>
    <string name="vvap_used">Used</string>
    <string name="vvap_engine_size_empty_error">Please enter a valid engine size.</string>
    <string name="vvap_engine_size_invalid_error">Engine size must be between 600cc and 3000cc.</string>
    <string name="vvap_gross_vehicle_value_valid_error">Please enter a valid gross vehicle mass.</string>
    <string name="vvap_gross_vehicle_value_invalid_error">Gross vehicle mass must be between 750kg and 3500kg.</string>
    <string name="vvap_tyre_rim_review_standard">Standard</string>
    <string name="vvap_tyre_rim_review_low_profile">Low-profile, run-flat or 4x4</string>

    <!-- VVAP done -->
    <string name="vvap_done_title">We have received your application for your Vehicle Value Added Products</string>
    <string name="vvap_done_title_reupload">We have received your photos for your Vehicle Value Added Products application</string>
    <string name="vvap_done_subtitle">We will email the outcome of your application to %s within 2 business days.</string>
    <string name="vvap_done_done_text2">We\'re verifying your application and photos.</string>
    <string name="vvap_done_done_text2_reupload">We\'re verifying your submitted photos.</string>
    <string name="vvaps_claim_title">We\'ve received the photos for your claim.</string>
    <string name="vvaps_claim_number_title">Your claim number is</string>


    <!-- VVAP Confirm details -->
    <string name="vvap_confirm_title">Please confirm your details</string>
    <string name="vvap_confirm_finance_details">FINANCE DETAILS</string>
    <string name="vvap_confirm_finance_house">Finance house</string>
    <string name="vvap_confirm_finance_account">Finance account number</string>
    <string name="vvap_confirm_finance_amount">Finance amount</string>
    <string name="vvap_confirm_agreement_start">Finance agreement start date</string>
    <string name="vvap_confirm_agreement_end">Finance agreement end date </string>
    <string name="vvap_confirm_ballon_payment">Is there a residual or balloon payment on your vehicle?  </string>
    <string name="vvap_confirm_residual_payment">Residual or balloon payment</string>
    <string name="vvap_confirm_insurer_details">INSURER DETAILS</string>
    <string name="vvap_confirm_insurer_name">Insurer name</string>
    <string name="vvap_confirm_policy_number">Policy number</string>
    <string name="vvap_confirm_insured_amount">Insured amount</string>
    <string name="vvap_confirm_effective_start_date">Insurer effective start date</string>
    <string name="vvap_confirm_info_1">To update your details</string>
    <string name="vvap_confirm_info_2">On the dashboard, click the policy you want to update, and then click Manage policy.</string>
    <string name="insurance_select_cause_damage">Select cause of damage</string>
    <string name="vvap_claim_wear_n_tear">Wear and tear</string>
    <string name="vvap_claim_mechanical_failure">Mechanical failure</string>
    <string name="vvap_claim_electrical_failure">Electrical failure</string>

    <!-- VVAP Policy admin: TyreRim and Dent and scratch -->
    <string name="vvap_claim_tyre">Tyre</string>
    <string name="vvap_claim_rim">Rim</string>
    <string name="vvap_claim_dent">Dent</string>
    <string name="vvap_claim_scratch">Scratch</string>
    <string name="vvap_claim_windscreen_chip">Windscreen chip</string>
    <string name="vvap_claim_windscreen_crack">Windscreen crack</string>
    <string name="vvap_claim_dent_tooltip">May not exceed 150 mm with no rip, perforation or tear on the panel.</string>
    <string name="vvap_claim_scratch_tooltip">May not exceed 200 mm, nor extend over more than two panels.</string>
    <string name="vvap_claim_windscreen_chip_tooltip">The size of a windscreen chip may not exceed 16 mm or the size of a 10c coin.</string>
    <string name="vvap_claim_windscreen_crack_tooltip">The size of a windscreen crack may not exceed 10 mm in length.</string>
    <string name="vvap_claim_pending_photos">Pending claim photos</string>
    <string name="vvap_claim_verifying_photos">Verifying claim photos</string>
    <string name="vvap_claim_manual_photo_upload">Pending manual photo upload</string>

    <!-- Review -->
    <string name="vvap_review_insurable_event">Your insurable event</string>
    <string name="vvap_review_date_incident">Date of incident</string>
    <string name="vvap_review_insurable_damage">What caused the damage?</string>
    <string name="vvap_review_insurable_what_happened">What happened?</string>
    <string name="vvap_review_insurable_contact_detail">Your contact details</string>
    <string name="vvap_review_insurable_contact_number">Contact number</string>
    <string name="vvap_review_insurable_email_address">Email address</string>
    <string name="vvap_review_insurable_submit_claim">Submit claim</string>
    <string name="vvap_your_contact_header">YOUR CONTACT DETAILS</string>
    <string name="vvap_confirm_contact_detail_title">Please confirm or update your contact details.</string>
    <string name="vvap_debit_order_note_yearly_transaction_text">The annual premium payment will be the date the policy is issued. E.g. if policy is issued on 17 March 2021, the payment due date will be 17 March 2021.</string>
    <string name="vvap_debit_order_note_monthly_transaction_text">Your full premium will be collected monthly on the date of your choice. The first debit will be collected immediately based on a pro-rata premium for the number of days cover provided for the first month. </string>
    <string name="vvap_review_vehicle_condition">Your vehicle condition</string>
    <string name="vvap_can_u_drive_label">Can you drive your vehicle?</string>
    <string name="vvap_where_located_vehicle_label">Where is your vehicle currently located?</string>
    <string name="vvap_location_of_vehicle_label">Location of your vehicle</string>

    <!-- Done -->
    <string name="vvap_done_reference"><![CDATA[Your reference number is <b>%1$s</b>]]></string>
    <string name="vvap_done_subtitle1">Acceptance/agreement of loss (AOL) from your insurer.</string>
    <string name="vvap_done_documents_need">Documents we\'ll need:</string>
    <string name="vvap_done_subtitle2">Settlement letter from your financial institution.</string>
    <string name="vvap_done_subtitle3">Amortisation statement from your financial institution.</string>
    <string name="vvap_done_subtitle4">Once our insurance experts have received your documents and reviewed your claim, we will let you know the outcome.</string>
    <string name="vvap_done_send_info">For more information send an email to </string>
    <string name="vvap_done_send_info_claim">For more information call us on</string>
    <string name="vvap_done_or_call_us">or call us on</string>
    <string name="vvap_done_send_email"> or send an email to</string>
    <string name="vvap_done_email_part1">Please send these documents to </string>
    <string name="vvap_done_email_part2">We\'ll also email this list shortly for your reference.</string>
    <string name="vvap_csf_done_email_body"><![CDATA[<p>Please find attached the outstanding documents.&nbsp;</p><ul><span>1) Amortization Statement</span><li><span>2) External Agreement Of Loss (AOL)</span></li><li><span>3) External Settlement Letter</span></li></ul><p><span>&nbsp;Kind regards</span></p><li><span>%1$s</span></li>]]></string>
    <string name="vvap_tlc_done_email_body"><![CDATA[<p>Please find attached the outstanding documents.&nbsp;</p><ul><span>1) Driver’s license</span><li><span>2) Towing invoice, if you arranged towing</span></li><li><span>3) Accident report/police report</span></li></ul><p><span>&nbsp;Kind regards</span></p><li><span>%1$s</span></li>]]></string>
    <string name="vvap_gap_done_email_body"><![CDATA[<p>Please find attached the outstanding documents.&nbsp;</p><ul><span>1) Authorization letter from underlying insurer</span><li><span>2) Repair quotation</span></li></ul><p><span>&nbsp;Kind regards</span></p><li><span>%1$s</span></li>]]></string>
    <string name="vvap_ew_cw_done_email_body"><![CDATA[<p>Please find attached the outstanding documents.&nbsp;</p><ul><span>1) Mechanical Damage Report</span><li><span>2) Picture of your current odometer reading</span></li><li><span>3) Repair Quotation</span></li><li><span>4) Vehicle Service history record</span></li></ul><p><span>&nbsp;Kind regards</span></p><li><span>%1$s</span></li>]]></string>

    <string name="vvaps_done_tlc_claim_text">Your SA driver\'s license.</string>
    <string name="vvaps_done_tlc_claim_aaranged">If you arranged towing, we\'ll need the towing invoice.</string>
    <string name="vvaps_done_tlc_claim_accident">Accident report/police report.</string>

    <!-- VVAP Policy admin: Manage policy -->
    <string name="vvap_admin_banking_title">Banking details</string>
    <string name="vvap_admin_finance_title">Finance details</string>
    <string name="vvap_admin_insurer_title">Insurer details</string>
    <string name="vvap_admin_payment_day_title">Payment day details</string>
    <string name="vvap_admin_premium_title">Premium Frequency Details</string>
    <string name="vvap_admin_vehicle_title">Vehicle details</string>
    <string name="vvap_admin_warranty_title">Warranty details</string>
    <string name="vvap_admin_warranty_option_title">Warranty option details </string>

    <!-- VVAP edit finance details -->
    <string name="vvap_finance_cover_type">Cover type/s</string>
    <string name="vvap_finance_policy_start_date">Policy start date</string>
    <string name="vvap_finance_edit_title">Edit finance details</string>
    <string name="vvap_payment_day_edit_tool_bar">Edit payment day</string>
    <string name="vvap_insurer_edit_title">Edit insurer details</string>
    <string name="vvap_vehicle_edit_title">Edit vehicle details</string>
    <string name="vvap_finance_edit_subtitle">Any changes to your financial details may result in a change in your premium amount.</string>
    <string name="vvap_finance_edit_agreement_accessibility">Finance agreement start date tool tip</string>
    <string name="vvap_finance_edit_agreement_end_accessibility">Finance agreement end date tool tip</string>
    <string name="common_date_picker_accessibility">Date picker</string>
    <string name="vvap_finance_edit_residual_accessibility">Residual or Balloon amount tool tip</string>
    <string name="vvap_finance_edit_save_changes">Save changes</string>
    <string name="vvap_review_new_monthly_premium">New monthly premium</string>
    <string name="vvap_review_new_premium">New premium</string>
    <string name="vvap_review_new_yearly_premium">New annual premium</string>
    <string name="vvap_review_old_monthly_premium">Old monthly premium</string>
    <string name="vvap_review_old_premium">Old premium</string>
    <string name="vvap_review_old_yearly_premium">Old annual premium</string>
    <string name="insurance_info_title">Please note</string>
    <string name="vvap_review_info_description_monthly">Your new monthly premium will take effect immediately.</string>
    <string name="vvap_review_info_no_change">There are no changes to your premium.</string>
    <string name="vvap_review_info_change">Switching to a non-Nedbank account means you will miss out on a 5% discount on your premium.\n\nYour new monthly premium will take effect immediately.</string>
    <string name="vvap_review_info_nedbank_change">Switching to a Nedbank account qualifies you for a 5% discount on your premium.\n\nYour new monthly premium will take effect immediately.</string>
    <string name="vvap_review_info_description_annual">Your new annual premium will take effect immediately.</string>
    <string name="vvap_finance_edit_review_title">Review your new premium</string>
    <string name="vvap_insurer_edit_subtitle">Updating your insurer details won\'t affect your premium amount.</string>
    <string name="vvap_insurer_edit_consent_text">I consent to Nedbank Insurance updating my insurer details.</string>
    <string name="vvap_vehicle_edit_consent_text">I consent to Nedbank Insurance updating my vehicle details.</string>
    <string name="vvap_insurance_cover_types">Cover type(s)</string>
    <string name="vvap_insurer_success_message">You\'ve successfully updated your insurer details.</string>
    <string name="vvap_insurer_failure_message">We couldn\'t update your insurer details.\nPlease try again later.</string>
    <string name="vvap_finance_success_message">You\'ve successfully updated your finance details.</string>
    <string name="vvap_finance_failure_message">We couldn\'t update your finance details.\nPlease try again later.</string>
    <string name="vvap_finance_decline_description">By declining, your updated finance details won\'t be saved and your premium will remain the same.</string>

    <!-- VVAP edit banking details -->
    <string name="vvap_confirm_banking_details">BANKING DETAILS</string>
    <string name="vvap_preferred_account">Preferred debit account</string>
    <string name="vvap_debt_bank_title">Nedbank Insurance will debit your preferred bank.</string>
    <string name="vvap_edit_banking_title">Nedbank Insurance will debit your preferred bank account.</string>
    <string name="vvap_consent_term_desc">I consent to Nedbank Insurance updating my banking details.</string>
    <string name="vvap_admin_policy_done">We\'re almost done</string>
    <string name="vvap_need_follow_txt">We just need the following:</string>
    <string name="vvap_need_follow_text">We need the following:</string>
    <string name="vvap_bank_statement_txt">Bank Statement</string>
    <string name="vvap_reference_email_txt">Look out for a reference email.</string>
    <string name="vvap_email_doc_txt">Please email this document to</string>
    <string name="vvap_with_policy_no_txt">with your policy number.</string>
    <string name="vvap_success_banking_msg">You\'ve successfully updated your banking details.</string>
    <string name="vvap_pending_documents_msg">Banking details update pending. Please submit the required document.</string>
    <string name="vvap_failure_banking_msg">We couldn\'t update your banking details. Please try again later</string>
    <string name="vvap_consent_description">I consent to Nedbank Insurance updating my finance details.</string>
    <string name="vvap_banking_edit_title">Edit banking details</string>
    <string name="vvap_check_account_txt">Please check your account details and save again.</string>
    <!--VVAP edit payment day details-->
    <string name="vvap_payment_day_edit_title">Edit payment day details</string>
    <string name="vvap_payment_day_details_header">PAYMENT DAY DETAILS</string>
    <string name="vvap_payment_day_txt">Payment day</string>
    <string name="vvap_payment_day_title">Nedbank Insurance will debit you on your preferred payment day.</string>
    <string name="vvap_consent_term_desc_payment">I consent to Nedbank Insurance updating my payment day details.</string>
    <string name="vvap_payment_day_success">You\'ve succcessfully updated your payment day details.</string>
    <string name="vvap_payment_day_failure">We couldn\'t update your payment day details. Please try again later.</string>
    <string name="vvap_payment_day_current_month_txt">This is confirmation of the change to your debit order. The change will apply from your next debt order.</string>
    <string name="vvap_payment_day_next_month_txt">Your debit order this month will be unchanged. Your change will apply from next month\'s debit order.</string>
    <string name="vvap_payment_day_debit_order_txt">Debit order change</string>
    <string name="vvap_payment_day_call_us">You cannot change the next payment date. For more information call us</string>
    <string name="vvap_payment_next_payment_date">Next payment date</string>
    <string name="pl_payment_day_details_info_text1">Select the day on which money is normally paid into your account to avoid missed premiums.</string>
    <string name="pl_payment_day_details_info_text2">Your full premium will be collected on your selected payment day along with any pro-rata premiums, if applicable</string>

    <!-- VVAP edit vehicle-->
    <string name="vvap_engine_type">Engine type</string>
    <string name="vvap_engine_size">Engine size</string>
    <string name="vvap_vehicle_details_info">If you would like to change the vehicle details above, call us 0800 333 111. </string>
    <string name="vvap_updating_vehicle_not_change_premium">Updating your vehicle details won\'t affect your premium amount. </string>

    <!-- VVAP Premium-->
    <string name="vvap_edit_premium_title">Edit premium details</string>
    <string name="vvap_premium_details_title">PREMIUM DETAILS</string>
    <string name="vvap_premium_when_do_you_pay">When do you pay</string>
    <string name="vvap_payment_day">Payment day</string>
    <string name="vvap_payment_title">Nedbank Insurance will debit you on preferred premium option.</string>
    <string name="vvap_payment_subtitle">When do you want to pay?</string>
    <string name="vvap_payment_info_text">You have changed to a monthly premium option. Your new payment day will be monthly.</string>
    <string name="vvap_payment_info_text_annual">You have changed to an annual premium option. Your new payment date will be the 1st of the following month every year.</string>
    <string name="vvap_payment_consent">I consent to Nedbank Insurance updating my premium details.</string>
    <string name="vvap_payment_review_info">Your new annual premium will take effect immediately.</string>
    <string name="vvap_payment_success_message">You\'ve successfully updated your premium details.</string>
    <string name="vvap_payment_failure_message">We couldn\'t update your premium details.\nPlease try again later.</string>
    <string name="vvap_payment_decline_title">Are you sure you want to decline?</string>
    <string name="vvap_payment_decline_description">By declining, your updated premium details won\'t be saved and your premium will remain the same.</string>
    <string name="authorization_letter_from_underlying_insurer">Authorization letter from underlying insurer.\nRepair quotation.</string>
    <string name="ew_cw_final_screen_message">Mechanic damage report.\n\nPicture of your current odometer reading.\n\nRepair quotation.\n\nVehicle service history record.</string>
    <string name="common_tooltip_txt">Tooltip</string>
    <string name="insurance_expert_review">Our insurance experts will now assess the damage and review your claim.</string>
    <string name="insurance_claim_subtext">We\'ll get back to you within \n3 business days.</string>
    <!-- VVAP warranty option detail-->
    <string name="vvap_warranty_option_title">Edit warranty option details</string>
    <string name="vvap_warranty_option_details_title">WARRANTY OPTION DETAILS</string>
    <string name="vvap_warranty_option_sub_title">Warranty option</string>
    <string name="vvap_warranty_option_success_message">You\'ve successfully updated your warranty option details.</string>
    <string name="vvap_warranty_option_failure_message">We couldn\'t update your warranty option details.\nPlease try again later.</string>
    <string name="vvap_edit_warranty_option_subtitle">Any changes to your warranty option details may result in a change in your premium amount.</string>
    <string name="vvap_edit_warranty_option_radio_button_title">Warranty option</string>
    <string name="vvap_edit_warranty_option_basic">Basic</string>
    <string name="vvap_edit_warranty_option_premium">Premium</string>
    <string name="vvap_edit_warranty_option_detail">Your selected warranty option covers your vehicle for:</string>
    <string name="vvap_edit_warranty_option_mechanical_failure">Mechanical failure</string>
    <string name="vvap_edit_warranty_option_electrical_failure">Electrical failure</string>
    <string name="vvap_edit_warranty_option_wear_tear_failure">Wear and tear</string>
    <string name="vvap_edit_warranty_option_cambelt_failure">Cambelt failure</string>
    <string name="vvap_edit_warranty_option_overheating_failure">Overheating</string>
    <string name="vvap_edit_warranty_option_supplementary_failure">Supplementary component cover</string>
    <string name="vvap_edit_warranty_option_consent">I consent to Nedbank Insurance updating my warranty option details.</string>
    <string name="vvap_edit_warranty_option_decline_description">By declining, your updated warranty option details won\'t be saved and your premium will remain the same.</string>
    <string name="vvap_edit_banking_decline_description">By declining, your updated banking details won\'t be saved and your premium will remain the same.</string>

    <!-- VVAP warranty detail-->
    <string name="vvap_edit_warranty_title">Edit warranty details</string>
    <string name="vvap_edit_warranty_sub_title">Updating your warranty details won\'t affect your premium amount.</string>
    <string name="vvap_warranty_details_title">WARRANTY DETAILS</string>
    <string name="vvap_warranty_covered_monthly_or_no_of_month">Covered monthly or a specified number of months</string>
    <string name="vvap_warranty_success_message">You\'ve successfully updated your warranty details.</string>
    <string name="vvap_warranty_detail_consent">I consent to Nedbank Insurance updating my warranty details.</string>
    <string name="vvap_warranty_failure_message">We couldn\'t update your warranty details.\nPlease try again later.</string>

    <!-- TLC Claim -->
    <string name="tlc_claim_what_you_claiming"> What are you claiming for?</string>
    <string name="tlc_claim_my_vehicle_total_loss">My vehicle (Total loss)</string>
    <string name="tlc_claim_third_party_liability">Third party liability</string>
    <string name="tlc_claim_dialog_title_third_party_liability">For any third party liability claims</string>
    <string name="tlc_claim_dialog_description">Contact us on 0800 333 111 or send an <NAME_EMAIL>.</string>
    <string name="tlc_claim_vehicle_condition">The vehicle\'s condition</string>
    <string name="tlc_claim_can_you_drive_vehicle">Can you drive your vehicle?</string>
    <string name="tlc_claim_where_vehicle_located">Where is your vehicle currently located?</string>
    <string name="tlc_claim_home">Home</string>
    <string name="tlc_claim_vehicle_location">Location of your vehicle</string>
    <string name="tlc_claim_pls_ensure_house_no_inclusion">Make sure you include the number of the house or building.</string>
    <string name="tlc_claim_postcodes_added_tooltip">Postcodes are added automatically and can’t be changed.</string>

    <!-- TLC Quote -->
    <string name="tlc_included_cover_title">Included covers</string>
    <string name="tlc_included_cover_dialog_title">Are you sure you would like to remove SASRIA cover?</string>
    <string name="tlc_included_cover_dialog_desc">You won\'t be covered against damage or loss during civil or public unrest, strikes, riots or acts of terror.</string>
    <string name="tlc_included_cover_subtitle">Included cover/s for total loss.</string>
    <string name="tlc_included_cover_sasria_text">Insures you against special risks such as civil or public unrest and acts of terrorism.</string>
    <string name="tlc_included_cover_tow_cost">Tow-in cost and safeguarding</string>
    <string name="tlc_included_cover_tow_cost_description1">If your vehicle is unable to be moved, we offer a selection of towing partners and storage facilities.</string>
    <string name="tlc_included_cover_tow_cost_description2">We\'ll compensate you for the costs up to the set limit to keep your vehicle secure, and move it to the closest repair centre, if you have a valid claim.</string>

    <!-- Dent and Scratch -->
    <string name="dent_scratch_title">MyCover Dent and Scratch</string>

    <!-- Tyre and Rim -->
    <string name="tyre_rim_review_front_tyre_size">Front tyre size</string>
    <string name="tyre_rim_review_back_tyre_size">Back tyre size</string>
    <string name="tyre_rim_review_are_vehicle_tyre_flat">Are your vehicle tyres run-flats?</string>
    <string name="tyre_rim_review_tyre_type">Tyre type</string>

    <!-- Dent and Scratch -->
    <string name="essential_review_full_service_history">Does your vehicle have a full service history?</string>
    <string name="essential_review_warranty_option">Warranty option</string>
    <string name="vvap_debit_dialog_title">Based on your payment day</string>
    <string name="vvap_debit_dialog_message">Because Nedbank Insurance collects premiums in advance, your account will be debited with two premiums this month.\n\nChoose another policy start date to prevent your account from being debited with two premiums.</string>
    <string name="vvap_debit_dialog_message_2">Your full premium will be collected on your selected payment day along with any pro-rata premiums, if applicable.</string>

    <!-- PL Combo changes -->
    <string name="pl_debit_order_monthly_premium_title">MONTHLY PREMIUM</string>
    <string name="pl_debit_order_annual_premium_title">ANNUAL PREMIUM</string>
    <string name="pl_debit_order_monthly_description">Nedbank Insurance will debit your preferred account with your monthly premium.</string>
    <string name="pl_debit_order_annual_description">Nedbank Insurance will debit your preferred account with your annual premium.</string>
    <string name="pl_debit_order_monthly_other_bank_info">Because Nedbank Insurance collects premiums a month in advance and based on your policy start date and the payment day, you may have to pay a pro-rated amount as well as this month\'s premium next month.\n\nPlease view the policy schedule for your premium amounts that will be due.</string>
    <string name="pl_debit_order_annual_nedbank_info">The annual premium payment day will be the policy start date. E.g. if the policy start date is on 17 March 2022, the payment due date will be 17 March 2023.</string>
    <string name="pl_debit_order_details_monthly_nedbank_info">You may be charged a pro-rated amount and your monthly premium.\n\nPlease view the policy schedule for your premium amounts that will be due.</string>
    <string name="pl_admin_premium_title">Premium frequency details</string>

    <!-- MCL App Update -->
    <string name="update_money_app_outdated_version">We\'ve noticed that you’re using an outdated version of the Money app.</string>
    <string name="update_money_app_visit_store">To ensure that we provide you with the most recent product offerings and features, please visit your app store and update the app to the latest version.</string>
    <string name="google_play">Google Play</string>
    <string name="insurance_not_now">Not now</string>

    <!-- PLCombo vehicle security blocker -->
    <string name="pl_motor_tracker_not_installed_error_title">Vehicles with a value of over R500 000 must have a tracking device installed</string>
    <string name="pl_motor_tracker_connect">Please call Tracker Connect (Pty) Ltd on </string>
    <string name="pl_motor_number"> 0860 605 040</string>
    <string name="pl_motor_send_email_to">or send an email to</string>
    <string name="pl_motor_email"><EMAIL></string>
    <string name="pl_motor_send_email_to_tracker_device">to have a tracking device installed in your vehicle.</string>
    <string name="pl_motor_return_quote">You can return for a quote once you have installed a tracking device in your vehicle.</string>

    <!-- Tyre rim photos -->
    <string name="vvap_tyre_rim_photos_title">Your tyre and rim photos</string>
    <string name="vvap_tyre_rim_photos_title_caps">YOUR TYRE AND RIM PHOTOS</string>
    <string name="vvap_tyre_rim_photos_desc">For your tyres and rims to be evaluated, we\'ll need clear photos of your tyres and rims.</string>
    <string name="vvap_tyre_rim_photos_subtitle">Here are things to keep in mind:</string>
    <string name="vvap_tyre_rim_photos_subtitle_point1">The photos cannot be too dark or too light.</string>
    <string name="vvap_tyre_rim_photos_subtitle_point2">Your vehicle must be clearly visible.</string>
    <string name="vvap_tyre_rim_photos_subtitle_point3">No details should be cut off.</string>
    <string name="vvap_tyre_rim_photos_header">TYRE AND RIM PHOTOS %s/4</string>
    <string name="vvap_tyre_rim_photos_header_title">Tyre and rim photos</string>
    <string name="vvap_tyre_rim_photos_header_unhappy">TYRE AND RIM PHOTOS %1s/%2s</string>
    <string name="vvap_tyre_rim_photos_front_left">Front left tyre and rim</string>
    <string name="vvap_tyre_rim_photos_front_right">Front right tyre and rim</string>
    <string name="vvap_tyre_rim_photos_back_left">Back left tyre and rim</string>
    <string name="vvap_tyre_rim_photos_back_right">Back right tyre and rim</string>
    <string name="vvap_tyre_rim_photos_no_photo">No photo</string>
    <string name="vvap_tyre_rim_photos_uploading">Uploading…</string>
    <string name="vvap_tyre_rim_photos_submit">Submit photos</string>
    <string name="vvap_tyre_rim_photos_dialog_title">Please note</string>
    <string name="vvap_tyre_rim_photos_dialog_description">We could verify only some of the photos that you have uploaded. Please upload all the required photos again to complete your application.</string>
    <string name="vvap_tyre_rim_photos_claim_dialog_desc">We could verify only some of the photos that you have uploaded. Please upload all the required photos again to complete your claim.</string>
    <string name="vvap_tyre_rim_photos_reupload_description">We were unable to verify your tyres and rims photos. Please provide clear photos of your tyres and rims.</string>
    <string name="vvap_policy_detail_info_title">There are outstanding claim photos on your policy. We need these photos to complete your claim.</string>

    <!-- Tyre rim photos upload prep -->
    <string name="vvap_tyre_rim_prep_title">We need some photos</string>
    <string name="vvap_tyre_rim_prep_desc">To complete your application, we\'ll need 4 clear photos of your tyres and rims:</string>
    <string name="vvap_tyre_rim_prep_1_1">Front left tyre and rim</string>
    <string name="vvap_tyre_rim_prep_1_2">Front right tyre and rim</string>
    <string name="vvap_tyre_rim_prep_1_3">Back left tyre and rim</string>
    <string name="vvap_tyre_rim_prep_1_4">Back right tyre and rim</string>
    <string name="vvap_tyre_rim_prep_2_1">Front of vehicle</string>
    <string name="vvap_tyre_rim_prep_desc_2">We will also need 8 clear photos of your vehicle:</string>
    <string name="vvap_tyre_rim_prep_2_2">Front left of vehicle</string>
    <string name="vvap_tyre_rim_prep_2_3">Front right of vehicle</string>
    <string name="vvap_tyre_rim_prep_2_4">Left side of vehicle</string>
    <string name="vvap_tyre_rim_prep_2_5">Right side of vehicle</string>
    <string name="vvap_tyre_rim_prep_2_6">Back of vehicle</string>
    <string name="vvap_tyre_rim_prep_2_7">Back left of vehicle</string>
    <string name="vvap_tyre_rim_prep_2_8">Back right of vehicle</string>
    <string name="vvap_tyre_rim_info_text">If we do not receive all the photos, we cannot process your application. Please make sure that you upload all the required photos.</string>
    <string name="vvap_tyre_rim_info_text_claim">If we do not receive all the photos, we cannot process your claim. Please make sure that you upload all the required photos.</string>
    <string name="vvap_dent_scratch_desc">To complete your application, we\'ll need 8 clear photos of your vehicle:</string>
    <string name="vvap_dent_scratch_desc_claim">To complete your claim, we\'ll need 8 clear photos of your vehicle:</string>
    <string name="vvap_dent_scratch_photos_reupload_description">We were unable to verify your vehicle photos. Please provide clear photos of your vehicle.</string>
    <string name="vvap_tyre_rim_desc_claim">To complete your claim, we\'ll need 4 clear photos of your tyres and rims:</string>
    <string name="vvap_claim_pending_photo">Pending photos</string>

    <!--vvaps tyre and rim camera prep-->
    <string name="error_camera_permission_required">Camera permission is required</string>
    <string name="error_start_camera">unable to start camera</string>
    <string name="remove_photo_title">Remove photo?</string>
    <string name="remove_photo_message">Are you sure you want to remove this photo?</string>
    <string name="please_note_photo_message">Your photos will not be saved and you will be required to provide us with your photos again.</string>
    <string name="rotate_camera_message">Rotate your phone to landscape view to take a photo.</string>
    <string name="retake_image">Retake</string>
    <string name="delete_image">Delete button</string>
    <string name="add_button">Add button</string>
    <string name="vehicle_icon">Vehicle icon </string>
    <string name="upload_photos">Upload photos</string>
    <string name="upload_photos_title">Photos</string>
    <string name="vvap_upload_fail_info_text">Please provide us with the outstanding photos within 7 days.\n\nIf we do not receive the photos, your policy will not appear on your dashboard and you will have to call 0800 333 111.</string>
    <string name="claim_upload_photo_info_text">Please provide us with the outstanding photos within 7 days.\n\nIf we don\'t receive the photos within 7 days, we will contact you to help you upload them.</string>
    <!--fraud alert-->
    <string name="fraud_alert_title">Sorry, you don\'t qualify for cover.</string>
    <string name="fraud_alert_why">Here’s why:</string>
    <string name="fraud_alert_subtitle">Misrepresentation, which means deception intended to result in financial or personal gain on a short-term insurance policy.</string>
    <string name="fraud_alert_email_phone">For more info contact 0800 333 111\nor send an email to\<EMAIL>.</string>
    <string name="fraud_alert_dashboard_subtitle">You can only view your past policies and access emergency services. We are unable to provide you with additional cover at this stage.</string>
    <string name="fraud_alert_dashboard_title">Sorry, you have limited functionality.</string>
    <!--insurance product-->
    <string name="funeral_cover_title">Build your own package</string>
    <string name="funeral_cover_desc">Cover of up to R100 000. Add up to 29 dependants.</string>
    <string name="fun_family_cover_title">Family package</string>
    <string name="fun_family_cover_desc">Cover of up to R30 000 for only R90 a month. Add up to 6 dependants.</string>
    <string name="fun_30k_cover_title">R30 000 individual package</string>
    <string name="fun_30k_cover_desc">Cover of up to R30 000 for only R50 a month.</string>
    <string name="fun_10k_cover_title">R10 000 individual package</string>
    <string name="fun_10k_cover_desc">Cover of up to R10 000 for only R30 a month.</string>
    <string name="life_cover_title">Life cover</string>
    <string name="life_accident_cover_title">Personal-accident cover</string>
    <string name="life_accident_cover_desc">Get protection for when the unexpected happens.</string>
    <string name="legal_expanse_title">Legal expenses cover</string>

    <string name="asset_my_vehicle_title">My vehicle</string>
    <string name="asset_my_vehicle_desc">Get comprehensive or additional cover for your vehicle.</string>
    <string name="asset_my_building_title">My buildings</string>
    <string name="asset_my_building_desc">Comprehensive cover for your home and structure.</string>
    <string name="asset_my_house_title">My house contents</string>
    <string name="asset_my_house_desc">Cover for valuables inside your home.</string>
    <string name="asset_vvap_title">Vehicle value-added products</string>
    <string name="asset_vvap_desc">Get additional cover for dents, scratches and other damages.</string>

    <string name="my_vehicle_title">Comprehensive\ncover</string>
    <string name="my_vehicle_desc">Get a vehicle insurance estimate in under 60 seconds.</string>

    <string name="vvap_value_added_product">Vehicle value-added products</string>
    <string name="vvap_total_loss_desc">Pays out the retail value of your vehicle if it is a total loss.</string>
    <string name="vvap_gap_desc">Covers wear and tear, overheating and cambelt failure.</string>
    <string name="vvap_shortfall_desc">Pays out if the vehicle is beyond economical repair following loss or damage.</string>
    <string name="vvap_comprehensive_warranty_desc">Covers more than 30 vehicle components.</string>
    <string name="vvap_essential_warranty_desc">Covers the 6 critical components of your vehicle.</string>
    <string name="vvap_tr_desc">Covers tyres and/or rims fitted to the vehicle.</string>
    <string name="vvap_ds_desc">Covers the interior and exterior of the vehicle.</string>

    <!--VVAPs education title-->
    <string name="total_loss_education_title">Cover for only theft and total write-off events if you don’t have comprehensive vehicle insurance.</string>
    <string name="credit_shortfall_education_title">Covers the difference between what you owe the bank and the insured value of your vehicle if it is written off or stolen.</string>
    <string name="gap_warranty_education_title">Provides protection against situations where your existing warranty does not cover the full cost.</string>
    <string name="comprehensive_warranty_education_title">Provides cover for a wide range of expensive mechanical and electrical components that are not covered under your normal vehicle insurance.</string>
    <string name="essential_warranty_education_title">Provides cover for limited mechanical and electrical components that is not covered under your normal vehicle insurance.</string>
    <string name="dent_n_scratch_education_title">Protects your vehicle against minor dents, scratches, stone chips and hail damage that you would not normally claim for from your vehicle insurance.</string>
    <string name="tyre_n_rim_education_title">Covers the cost of repairing or replacing damaged tyres and rims that is not covered under your normal vehicle insurance.</string>

    <!--vvaps education screen product title-->
    <string name="vvap_done_title_vvaps">We have received your application for </string>
    <string name="pl_success_screen_title">We have received your application</string>

    <string name="sorry_not_qualify_for">Sorry, you don\'t qualify for</string>
    <string name="sorry_not_qualify_for_insurance">Sorry, you don\'t qualify for this insurance.</string>
    <string name="reason_why_not_qualify">Reasons why you don\'t qualify:</string>
    <string name="history_decline_n_claim_policy_info">You have had judgments against you, or an insurer has canceled your policy or declined to cover you.\n\nYou have had more than 3 claims in the last 12 months.</string>
    <string name="history_decline_policy_info">You have had judgments against you, or an insurer has canceled your policy or declined to cover you.</string>
    <string name="history_claim_policy_info">You have had more than 3 claims in the last 12 months.</string>
    <string name="total_loss_cover">Total loss cover</string>
    <string name="credit_short_fall_cover">Credit shortfall cover</string>
    <string name="gap_warranty_cover_cover">Gap warranty cover</string>
    <string name="comprehensive_warranty_cover">Comprehensive warranty cover</string>
    <string name="essential_warranty_cover">Essential warranty cover</string>
    <string name="dent_scratch_cover"> Dent and scratch cover</string>
    <string name="tyre_rim_cover">Tyre and rim cover</string>

    <string name="reasons_why_you_may_not_qualify">Reasons why you may not qualify:</string>
    <string name="not_cover_exotic_vehicle">The policy does not cover exotic vehicles.</string>
    <string name="vvap_done_title_vvaps_product">your Vehicle Value Added Products</string>

    <!--PL combo -->

    <string name="pl_vehicle_cover_title_small">Vehicle cover</string>
    <string name="pl_buildings_cover_title_small">Buildings cover</string>
    <string name="pl_house_contents_cover_title_small">House contents cover </string>

    <!--quote hard stop text and before we continue question-->
    <string name="pl_quote_cancellations">Have you had any judgements or insurance policy cancellations since %1$s? </string>
    <string name="hhc_save_application_cancellations">Have you been declined or had your policy cancelled by an insurer since %1$s? </string>
    <string name="pl_quote_claim_since">Have you submitted any claims since %1$s?</string>
    <string name="unfortunately_proceed_with_quote">Unfortunately, we cannot proceed with this quote, because it is no longer valid.</string>
    <string name="unfortunately_proceed_with_application">Unfortunately, we cannot proceed with this application, because it is no longer valid.</string>
    <string name="unfortunately_proceed_with_quote_sub_title_pl">You have submitted a claim or had a cancellation or judgement since this quote was saved. This could impact your premium.</string>
    <string name="unfortunately_proceed_with_birthday_quote_pl">You have had a birthday since this quote was saved. This could impact the premium.</string>
    <string name="for_more_info_call_us">For more information call us on 0800 333 111.</string>
    <string name="new_quote">New quote</string>
    <string name="new_application">New application</string>

    <string name="unfortunately_proceed_with_quote_sub_title_nifp">You have submitted a claim since this application was saved. This could impact your premium.</string>
    <string name="unfortunately_proceed_with_birthday_you_quote_nifp">You have had a birthday since this quote was saved. This could impact the premium.\n\nPlease complete a new quote.</string>
    <string name="unfortunately_proceed_with_birthday_your_dependant_quote_nifp">Your dependant/s have had a birthday since this quote was saved. This could impact the premium.\n\nPlease complete a new quote.</string>
    <string name="unfortunately_proceed_with_birthday_you_n_dependant_quote_nifp">You and your dependant/s have had a birthday since this quote was saved. This could impact the premium.\n\nPlease complete a new quote.</string>
    <string name="unfortunately_proceed_with_cover_amount_quote_nifp">Your existing cover, together with the cover amount of this quote, exceeds R100 000.\n\nPlease complete a new quote.</string>


    <string name="unfortunately_proceed_with_cover_amount_application_hhc">You have submitted a claim, been declined or had your policy cancelled by an insurer since this application was saved. This could impact your premium.</string>


    <!--Legal Expense-->
    <string name="preexisting_legal_matters">Pre-existing legal matters</string>
    <string name="legal_action_question">Do you have any ongoing or pending legal action?</string>
    <string name="legal_expense_note">Legal expenses related to pre-existing matters that have started before the start date of your policy will not be covered.</string>

    <string name="legal_expense_understand">I understand</string>
    <string name="please_describe_legal_action">Please describe the legal action</string>
    <string name="describe_legal_action_sentences">Describe in 1 or 2 sentences.</string>

    <string name="waiting_period_txt">waiting periods</string>
    <string name="waiting_period_title_for_following">The following waiting periods apply to this cover:</string>

    <!--legal expense success -->
    <string name="your_application_reference">Your application reference number is\n</string>
    <string name="feedback_to_email">We\’ll send feedback to %s within 2 business days.</string>
    <string name="switching_policies">If switching policies</string>
    <string name="keep_your_current_policy">Keep your current policy active until your Nedbank cover is confirmed.</string>
    <string name="for_more_information_call_us">For more information, call us on %1$s or send an email to %2$s.</string>

    <!--Legal expenses quote flow-->
    <string name="before_we_continue_legal_action">Do you have any ongoing or pending legal action?</string>

    <string name="insurance_dashboard_sub_title">Choose the policy that you would like to manage.</string>
    <string name="insurance_dashboard_sub_title_claim">Choose the policy that you would like to manage or submit a claim for.</string>
    <string name="insurance_dashboard_manage_policy_title">Manage policy</string>
    <string name="insurance_dashboard_policy_instruction">You can only select one policy at a time.</string>


    <string-array name="insurance_category_title">
        <item>@string/funeral</item>
        <item>@string/life</item>
        <item>@string/valuables</item>
        <item>@string/legal_expense</item>
    </string-array>
    <string-array name="insurance_category_desc">
        <item>@string/funeral_desc</item>
        <item>@string/life_desc</item>
        <item>@string/valuables_desc</item>
        <item>@string/legal_expense_desc</item>
    </string-array>
    <!--insurance funeral product-->
    <string-array name="funeral_product_list_title">
        <item>@string/funeral_cover_title</item>
        <item>@string/fun_family_cover_title</item>
        <item>@string/fun_30k_cover_title</item>
        <item>@string/fun_10k_cover_title</item>
    </string-array>
    <string-array name="funeral_product_list_desc">
        <item>@string/funeral_cover_desc</item>
        <item>@string/fun_family_cover_desc</item>
        <item>@string/fun_30k_cover_desc</item>
        <item>@string/fun_10k_cover_desc</item>
    </string-array>
    <!--insurance life product-->
    <string-array name="life_product_list_title">
        <item>@string/life_cover_title</item>
        <item>@string/life_accident_cover_title</item>
    </string-array>
    <string-array name="life_product_list_desc">
        <item>@string/life_desc</item>
        <item>@string/life_accident_cover_desc</item>
    </string-array>
    <!--insurance my assets product-->
    <string-array name="asset_product_list_title">
        <item>@string/asset_my_vehicle_title</item>
        <item>@string/asset_my_building_title</item>
        <item>@string/asset_my_house_title</item>
    </string-array>
    <string-array name="asset_product_list_desc">
        <item>@string/asset_my_vehicle_desc</item>
        <item>@string/asset_my_building_desc</item>
        <item>@string/asset_my_house_desc</item>
    </string-array>
    <!--insurance myVehicle product list-->
    <string-array name="my_vehicle_product_list_title">
        <item>@string/my_vehicle_title</item>
        <item>@string/asset_vvap_title</item>
    </string-array>
    <string-array name="my_vehicle_product_list_desc">
        <item>@string/my_vehicle_desc</item>
        <item>@string/asset_vvap_desc</item>
    </string-array>
    <!--insurance VVAP product list-->
    <string-array name="vvap_product_list_title">
        <item>@string/vvap_total_loss_title</item>
        <item>@string/vvap_gap_warranty_title</item>
        <item>@string/vvap_credit_shortfall_title</item>
        <item>@string/vvap_comprehensive_warranty_title</item>
        <item>@string/vvap_essential_warranty_title</item>
        <item>@string/vvap_tyre_rim_title</item>
        <item>@string/vvap_dent_scratch_title</item>
    </string-array>
    <string-array name="vvap_product_list_desc">
        <item>@string/vvap_total_loss_desc</item>
        <item>@string/vvap_gap_desc</item>
        <item>@string/vvap_shortfall_desc</item>
        <item>@string/vvap_comprehensive_warranty_desc</item>
        <item>@string/vvap_essential_warranty_desc</item>
        <item>@string/vvap_tr_desc</item>
        <item>@string/vvap_ds_desc</item>
    </string-array>

    <string-array name="ten_k_education_items">
        <item>Funeral cover for yourself with free benefits and services for peace of mind.</item>
        <item>Your benefits don\'t lapse if you can pay only part of your premiums.</item>
        <item>You have a 30-day grace period to pay your premium.</item>
        <item>1 x month free cover if you pay 11 full premiums in a row.</item>
    </string-array>
    <string-array name="thirty_k_education_items">
        <item>Funeral cover for yourself with free benefits and services for peace of mind.</item>
        <item>No medical tests required.</item>
        <item>1 x month free cover if you pay 11 full premiums in a row.</item>
    </string-array>
    <string-array name="family_education_items">
        <item>Add up to 6 dependants.</item>
        <item>Funeral cover for you and up to 6 family members with free benefits and services when you need it most.</item>
        <item>1 x month free cover if you pay 11 full premiums in a row.</item>
        <item>Your policy will not lapse if you can pay only part of your premiums.</item>
    </string-array>
    <string-array name="build_own_cover_education">
        <item>Immediate cover for accidental death.</item>
        <item>Cover for up to 29 dependants.</item>
        <item>1 x month free cover if you pay 11 full premiums in a row.</item>
        <item>Discount of up to 10% on your premiums in advance.</item>
    </string-array>
    <string-array name="my_cover_education_items">
        <item>Automatic 5% discount if you apply now.</item>
        <item>Up to 10% in loyalty discounts available to Nedbank clients.</item>
        <item>Refund of your premium if you choose to cancel this policy within 31 days of its start date if we have not paid out a claim.</item>
    </string-array>
    <string-array name="personal_accident_education_items">
        <item>Cover for disappearance of an insured person.</item>
        <item>Cover for bereavement and repatriation cost.</item>
    </string-array>

    <!--VVAPs Product education data-->
    <!--total loss-->
    <string-array name="total_loss_education_items">
        <item>Pays out the retail value of your vehicle if it is a total loss.</item>
        <item>Covers towing and storage.</item>
        <item>Covers third-party liability up to specified maximum amount.</item>
    </string-array>

    <!--Credit shortfall-->
    <string-array name="credit_short_fall_education_items">
        <item>Pays out if the vehicle is beyond economical repair following loss or damage.</item>
        <item>Pays out if the vehicle has been stolen and not recovered within a reasonable period.</item>
    </string-array>

    <!--Gap warranty-->
    <string-array name="gap_warranty_education_items">
        <item>Covers wear and tear, overheating and cambelt failure.</item>
        <item>Covers mechanical and/or electrical failure.</item>
        <item>Includes supplementary component cover.</item>
    </string-array>

    <!--Comprehensive warranty-->
    <string-array name="comprehensive_warranty_education_items">
        <item>Covers more than 30 vehicle components.</item>
        <item>Covers all lubricated components of the engine.</item>
        <item>Cover against breakdowns due to overheating.</item>
        <item>Cover against engine failure.</item>
    </string-array>

    <!--Essential warranty-->
    <string-array name="essential_warranty_education_items">
        <item>Covers the 6 critical components of your vehicle.</item>
        <item>Covers all lubricated components of the engine.</item>
        <item>Cover against breakdowns due to overheating.</item>
        <item>Cover against engine failure.</item>
    </string-array>

    <!--Dent and scratch-->
    <string-array name="dent_n_scratch_education_items">
        <item>Covers the interior and exterior of the vehicle.</item>
    </string-array>

    <!--Tyre and rim-->
    <string-array name="tyre_n_rim_education_items">
        <item>Covers tyres and/or rims fitted to the vehicle.</item>
        <item>Includes a wheel-balancing and alignment benefit.</item>
    </string-array>

    <!--PL vehicle cover-->
    <string-array name="pl_vehicle_cover_items">
        <item>Cashback of 10% of your paid premiums for every 3 claim-free years.</item>
        <item>24/7 roadside and towing services.</item>
        <item>Various car hire options.</item>
        <item>Free 24hour legal assistance.</item>
        <item>Free trauma and assault cover.</item>
    </string-array>

    <!--PL Buildings cover-->
    <string-array name="Pl_buildings_cover_items">
        <item>24-hour services from pre-approved suppliers.</item>
        <item>Personal liability insurance for bodily injury or property damage.</item>
        <item>Cover for geysers and pipes included.</item>
    </string-array>

    <!--PL House contents cover -->
    <string-array name="Pl_house_contents_cover_items">
        <item>Cover for food spoilage due to power outages.</item>
        <item>Optional power surge cover for electrical items.</item>
        <item>Alternative accommodation while your home is inhabitable.</item>
    </string-array>

    <string-array name="what_we_cover_title">
        <item>""</item>
        <item>Civil legal action</item>
        <item>Defence against criminal charges</item>
        <item>Labour court legal action</item>
        <item>Family matters</item>
        <item>Identity theft</item>
        <item>Administration and finalisation of your estate</item>
    </string-array>
    <string-array name="what_we_cover_desc">
        <item>We will compensate you for the below-mentioned events, provided they happen or start after the waiting periods and were approved in writing:</item>
        <item>Civil legal action brought by or against you in your private capacity.</item>
        <item>If you are charged with a criminal offence, including the cost of a bail application and the bail amount. </item>
        <item>Legal action brought by or against you, including Commission for Conciliation, Mediation and Arbitration (CCMA) hearings.</item>
        <item>Legal action regarding divorce, child custody disputes, maintenance suits, access to children etc.</item>
        <item>Identity theft leading to real or potential prejudice, including the legal liability that results from an unknown person or institution using your personal information fraudulently. </item>
        <item>Following your accidental death, legal expenses related to the administration and finalisation of your estate will be paid into your estate or to your nominated beneficiary.\n\nCover amounts are included in the policy schedule.</item>
    </string-array>

    <string-array name="waiting_period_title">
        <item>Family matters</item>
        <item>All other matters</item>
        <item>Legal advice and support services</item>
        <item>Accidental death</item>
    </string-array>

    <string-array name="waiting_period_desc">
        <item>6 months from the policy start date.</item>
        <item>3 months from the policy start date.</item>
        <item>None</item>
        <item>None</item>
    </string-array>

    <!--legal expanses cover -->
    <string-array name="legal_expanse_items">
        <item>Legal cover for expenses related to criminal, family, labour and civil matters.</item>
        <item>Cover for bail-related costs.</item>
        <item>Access to 24-hour legal advice and support services.</item>
        <item>Drafting of selected contracts and other legal documents.</item>
    </string-array>

    <!-- Legal expense -->
    <string name="legal_expense_title">Set up your cover</string>
    <string name="legal_expense_subtitle">How much cover would you like?</string>
    <string name="legal_expense_your_cover_title">YOUR COVER</string>
    <string name="legal_expense_total_cover_amount">Total cover amount</string>
    <string name="legal_expense_included_in_cover">INCLUDED IN YOUR COVER</string>
    <string name="legal_expense_bail">Bail</string>
    <string name="legal_expense_bail_desc">Costs that apply for your temporary release from jail after you\’ve been arrested.</string>
    <string name="legal_expense_accidental_death">Accidental death</string>
    <string name="legal_expense_accidental_death_desc">Legal expenses related to the winding-up of your estate following your accidental death.</string>
    <string name="legal_expense_info">You could qualify for a discount of up to 15% on the premium in the next 2 steps.</string>
    <string name="legal_expense_75k">R75K</string>
    <string name="legal_expense_100k">R100K</string>
    <string name="legal_expense_150k">R150K</string>
    <string name="legal_expense_200k">R200K</string>
    <string name="legal_expense_75k_value">R75 000</string>
    <string name="legal_expense_100k_value">R100 000</string>
    <string name="legal_expense_150k_value">R150 000</string>
    <string name="legal_expense_200k_value">R200 000</string>
    <string name="legal_expense_up_to_10k">Up to R10 000</string>
    <string name="legal_expense_up_to_15k">Up to R15 000</string>
    <string name="legal_expense_up_to_20k">Up to R20 000</string>
    <string name="legal_expense_premium_195">R74.25 pm</string>
    <string name="legal_expense_premium_245">R90.00 pm</string>
    <string name="legal_expense_premium_295">R120.00 pm</string>
    <string name="legal_expense_premium_315">R150.00 pm</string>
    <string name="legal_expense_tooltip">We will pay legal and resolution expenses up to the selected total cover amount for an insurable \n event.</string>
    <string name="legal_expense_review_your_cover">About your cover</string>
    <string name="legal_expense_review_total_cover">Total cover</string>
    <string name="legal_expense_review_total_cover_value">%s per insurable event</string>
    <string name="legal_expense_review_title">%s cover</string>
    <string name="legal_expense_contact_phone">0861 111 323</string>
    <string name="legal_expense_contact_email"><EMAIL></string>
    <string name="legal_expense_description">We will cover legal expenses up to the selected total cover amount for an insured event.</string>
    <string name="legal_expense_cover_value_75000">Up to R75 000</string>
    <string name="legal_expense_cover_value_100000">Up to R100 000</string>
    <string name="legal_expense_cover_value_150000">Up to R150 000</string>
    <string name="legal_expense_cover_value_200000">Up to R200 000</string>

    <string name="legal_expenses_name_title">Legal expenses</string>
    <string name="tlc_name_title">To track progress of a claim</string>
    <string name="legal_expense_contact_label">24/7 legal advice contact details</string>
    <string name="emergency_assistance_contact">24/7 emergency assistance</string>
    <string name="legal_expense_claim_text">You can claim only for legal expenses that we have approved in writing.</string>
    <string name="pl_claim_subtitle">Vehicle, building, house content, valuables or personal accidental claims</string>

    <!-- Driver info confirmation and manual capture -->
    <string name="driver_license_information">Provide driver\’s licence information</string>
    <string name="confirm_driver_license_information">Confirm driver\’s licence information</string>
    <string name="driver_license_information_subtitle">You can find this information on the front of your licence card.</string>
    <string name="driver_license_number">Driver\'s license number</string>
    <string name="driver_license_issue_date">First issue date</string>
    <string name="driver_license_expiry_date">Expiry date</string>
    <string name="driver_license_code">License code</string>
    <string name="driver_license_info_manual_capture_title">Provide driver\'s license information</string>
    <string name="driver_license_manual_info_number">Licence number can only consist of letters (A–Z) and numbers (0–9).</string>
    <string name="driver_license_manual_issue_date">The issue date must be at least 18 years after your date of birth.</string>
    <string name="driver_license_manual_expiry_date">The expiry date cannot be earlier than the issue date.</string>
    <string name="driver_license_number_error">Driver\'s licence numbers must include alphanumeric characters only.</string>
    <string name="driver_license_number_error_length">Please enter a valid Licence number with 12 characters.</string>
    <string name="driver_license_number_expire_note_message">Your driver\'s licence is expiring today. Make sure that you renew it at your local traffic department before it expires.</string>
    <string name="driver_license_number_label">Driver\'s license number</string>
    <string name="driver_license_issue_date_label">Issue date</string>
    <string name="driver_license_expiry_date_label">Expiry date</string>
    <string name="driver_license_license_code_label">License code</string>

    <string name="your_estimated_premium">Your estimated premium</string>
    <string name="estimated_premium_options">Here is your monthly premium estimate.</string>
    <string name="estimated_monthly_premium_note">This premium is currently based on an estimated excess of R7&#160;500. You\’ll be able to adjust your excess later to find an amount that suits you best.</string>
    <string name="car_insurance_question">Do you have a car insurance right now?</string>
    <string name="car_insurance_please_note_info">If you are a main-banked Nedbank client and complete your application here, you could get up to 10% off on your premium.\n\nThis quote may change based on your circumstances and the cover options you choose.</string>
    <string name="estimated_premium_options_data">R 175.91</string>

    <!--Park and Retrieve Funeral / save application-->
    <string name="insurance_save_application">Save application</string>
    <string name="application_not_save">Application not saved</string>
    <string name="insurance_retrieve_application">Retrieve application</string>
    <string name="retrieve_not_fetched">Retrieve details not fetched</string>
    <string name="save_application_message">Would you like to save your progress? Your application will be valid for 30 days.</string>
    <string name="saved_application_info_title">You have saved application(s)</string>
    <string name="saved_application_info_subtitle">View saved applications</string>
    <string name="application_saved_title">Application saved</string>
    <string name="application_saved_subtitle">Your application has been saved.</string>
    <string name="application_delete_title">Application deleted</string>
    <string name="application_delete_subtitle">Your application has been deleted.</string>
    <string name="application_failed_delete">Application failed to delete.</string>
    <string name="try_again_snackbar">Try Again</string>
    <string name="application_not_delete">Application not deleted</string>
    <string name="please_complete_quote">Please complete a new quote.</string>
    <string name="please_complete_application">Please complete a new application.</string>
    <string name="application_failed_save">Application failed to save.</string>
    <string name="insurance_save_application_instruction">Your application has been saved and is valid for 30 days.</string>
    <string name="delete_api_error_msg">We are unable to delete your application at this time.\n\nPlease try again later. </string>
    <string name="save_api_error_msg">We are unable to save your application at this time.\n\nPlease try again later. </string>
    <string name="for_more_info_call_us_on">For more information call us on\n</string>
    <string name="complete_flow_txt">Complete</string>
    <string name="save_application_build_cover_info">In order to add more products or continue with the application, please make sure that you have completed any incomplete product information or delete the product.</string>

    <string name="pl_vehicle_hard_stop_modification_reason">You have made performance-enhancing modifications to your vehicle.</string>
    <!--vehicle modification-->

    <string name="pl_vehicle_modification">Your vehicle performance modification</string>
    <string name="pl_vehicle_please_note_info">This refers to modifications to enhance speed and handling, excluding aesthetics.</string>
    <string name="pl_vehicle_modification_performance">Have you made any performance-enhancing modifications to your vehicle?</string>

    <!--insurance dashboard claims and policies-->
    <string name="insurance_submit_claim">Submit claims</string>
    <string name="manage_policies">Manage policies</string>
    <string name="complete_applications">Complete applications</string>
    <string name="complete_update_your_quote">Complete or update your quote.</string>
    <string name="emergency_services_title">Emergency services</string>
    <string name="emergency_services_desc">Information on whom to call when you have an emergency.</string>
    <string name="emergency_services_note_info">These emergency services are available 24/7 to help you based on your cover.</string>
    <string name="emergency_services_roadside_assistance">Roadside assistance</string>
    <string name="emergency_services_roadside_assistance_number">0861 262 636</string>
    <string name="emergency_services_geyser_pipes">Geyser and pipes</string>
    <string name="emergency_services_geyser_pipes_number">0800 333 111</string>
    <string name="emergency_services_security_risk">Security risk</string>
    <string name="emergency_services_security_risk_number">0800 333 111</string>
    <string name="emergency_services_fire">Fire</string>
    <string name="emergency_services_fire_number">0800 333 111</string>
    <string name="insurance_ew_cw_contact_email"><EMAIL></string>
    <string name="insurance_home_owner_email"><EMAIL></string>
    <string name="insurance_ew_cw_contact_phone_no">0861 666 482</string>
    <string name="manage_your_claims">Manage your claims.</string>
    <string name="dashboard_policies_and_claim">Policies and claims</string>
    <string name="insurance_what_new">What’s new</string>
    <string name="insurance_for_new_widget">For you</string>
    <string name="cover_vehicle_dashboard_title">Take care of your loved ones. Get life cover of up to R3 million.</string>
    <string name="insurance_offer_title_txt">Your personalised offers.</string>
    <string name="insurance_offer_already_cover_title">You are already covered</string>
    <string name="insurance_offer_already_cover_desc">You cannot apply for this product again as you are already covered.\n\nFor more info call 0800 333 111.</string>
    <string name="manage_update_your_policies">Manage cover and submit your claims.</string>
    <string name="enhance_mobile_banking">Enhance your mobile-banking experience</string>
    <string name="go_google_play">Go to Google Play</string>
    <string name="insurance_dashboard_thank_you_title">Thank you for your interest in our products</string>
    <string name="insurance_dashboard_not_qualify">You don\'t currently qualify for any Nedbank Insurance products.</string>
    <string name="insurance_dashboard_you_contact_us">If you think there\'s been a mistake, call us on\n0800 333 111.</string>
    <string name="insurance_insurance_applicable_contact_us">For more details call us on 0800 333 111.</string>
    <string name="insurance_submit_a_claim">To submit a claim</string>
    <string name="submit_a_claim_toggle_screen">Submit a claim</string>
    <string name="policy_claim_title">Policies and claims</string>
    <string name="insurance_your_policies_title">Your policies</string>
    <string name="insurance_your_policies_desc">Manage cover, policies and personal details.</string>
    <string name="insurance_submit_claims_title">Submit claims</string>
    <string name="insurance_submit_claims_desc">Manage and submit your claims.</string>
    <string name="insurance_retrieve_snackbar_title">Something went wrong on our side</string>
    <string name="insurance_retrieve_snackbar_desc">We are unable to retrieve your policies at this time. Please try again.</string>
    <string name="insurance_account_snackbar_desc">We are unable to retrieve your account at this time. Please try again.</string>
    <string name="confirm_select_changes">Confirm selection change</string>
    <string name="you_are_about_selection">You are about to change your original selection, please confirm?</string>
    <string name="level_up_offer">Level up your finances with the assurance that your overdraft will be settled if life throws you a curveball.</string>
    <string name="debt_protection">Debt protection: Overdraft</string>
    <string name="insurance_for_you">For you</string>
    <string name="offer_education_not_interested">Not interested</string>
    <string name="offer_overdraft_current_limit">Current overdraft limit</string>
    <string name="offer_overdraft_monthly_premium">Monthly premium</string>
    <string name="offer_overdraft_payment_day">Payment day</string>
    <string name="offer_overdraft_account">Account</string>
    <string name="offer_overdraft_account_number">Account number</string>
    <string name="offer_overdraft_info">Calculated at R3.30 per R1 000 of the overdraft amount or part thereof.</string>
    <string name="offer_overdraft_tnc">I have read, understood and accept the Nedbank Overdraft assurance</string>
    <string name="offer_overdraft_title">Overdraft Assurance</string>
    <string name="offer_overdraft_subtitle">Protect your family from repaying your overdraft outstanding balance in the event of death, job loss or disability.</string>
    <string name="offer_overdraft_limit_value">50000</string>
    <string name="offer_overdraft_monthly_premium_value">300</string>
    <string name="offer_overdraft_payment_day_value">20</string>
    <string name="offer_overdraft_account_value">Elite Plus Current Account</string>
    <string name="offer_overdraft_accountNumber_value">4567 666 8888 1733</string>
    <string name="offer_setup_cover_premium">%s pm</string>

    <string name="my_cover_yourself_title">Tell us about yourself</string>
    <string name="my_cover_yourself_description">Please answer a few medical and lifestyle questions honestly, so we can provide you with cover that suits your needs.</string>
    <string name="my_cover_tell_about_yourself_title">Tell us about yourself</string>

    <string name="claim_discount">Choose which of the following discounts apply to you:</string>
    <string name="label_premium_monthly">Your monthly premium and discounts</string>
    <string name="title_cover_amount">Choose your cover amount</string>

</resources>

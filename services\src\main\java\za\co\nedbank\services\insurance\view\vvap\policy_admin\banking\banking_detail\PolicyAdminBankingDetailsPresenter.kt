package za.co.nedbank.services.insurance.view.vvap.policy_admin.banking.banking_detail

import android.annotation.SuppressLint
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.tracking.Analytics
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.tracking.adobe.AdobeContextData
import za.co.nedbank.core.utils.StringUtils

import za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyDetailResponseDataModel
import za.co.nedbank.services.insurance.domain.usecase.personal_lines.PLPolicyDetailUseCase
import za.co.nedbank.services.insurance.domain.usecase.vvap.claim.VVAPPolicyDetailUseCase
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.model.request.vvaps.claim.policy_detail.PolicyDetailRequestViewModel
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.BankAccountViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyDetailResponseViewModel
import za.co.nedbank.services.insurance.view.vvap.policy_admin.base.VVAPAdminPolicyBasePresenter
import za.co.nedbank.services.insurance.view.vvap.policy_admin.other.helper.PolicyFlowFlags
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget
import javax.inject.Inject

open class PolicyAdminBankingDetailsPresenter @Inject constructor(
    private val mAnalytics: Analytics,
    private val mNavigationRouter: NavigationRouter,
    private val policyDetailUseCase: VVAPPolicyDetailUseCase,
    private val plPolicyDetailUseCase: PLPolicyDetailUseCase,
    private val requestViewToDataMapper: PolicyDetailRequestViewToDataMapper,
    private val responseDataToViewMapper: PolicyDetailResponseDataToViewMapper
) : VVAPAdminPolicyBasePresenter<PolicyAdminBankingDetailsView>(
    policyDetailUseCase, requestViewToDataMapper, responseDataToViewMapper
) {
    private var mRiskSerialNumber: String? = null

    @SuppressLint("CheckResult")
    fun getPolicyDetails(flags: PolicyFlowFlags) {
        showViewProgressBar(true)
        if (flags.isPolicyPL) {
            mRiskSerialNumber = InsuranceConstants.ONE_STRING
            plPolicyDetailUseCase.execute(
                requestViewToDataMapper.map(
                    PolicyDetailRequestViewModel(
                        "BankingDetails", flags.policyNumber, InsuranceConstants.ONE_STRING
                    )
                )
            ).compose(bindToLifecycle()).doOnTerminate { showViewProgressBar(false) }
                .subscribe({ policyResponseData: PolicyDetailResponseDataModel ->
                    this.handlePolicyDetailResponse(policyResponseData)
                }, {
                    showAPIError()
                })
        } else {
            getRiskSerialNumber(flags.policyNumber, when {
                flags.isGapFlow -> InsuranceConstants.ONE_STRING
                flags.isTlcFlow -> InsuranceConstants.TWO_STRING
                flags.isDentScratchFlow -> InsuranceConstants.THREE_STRING
                flags.isTyreRimFlow -> InsuranceConstants.FOUR_STRING
                flags.isEssentialFlow -> InsuranceConstants.FIVE_STRING
                flags.isComprehensiveFlow -> InsuranceConstants.SIX_STRING
                else -> InsuranceConstants.ZERO_STRING
            }, callback = {
                this.mRiskSerialNumber = it
                policyDetailUseCase.execute(
                    requestViewToDataMapper.map(
                        PolicyDetailRequestViewModel(
                            "BankingDetails",
                            flags.policyNumber,
                            rikSerialNumber = it
                        )
                    )
                ).compose(bindToLifecycle()).doOnTerminate { showViewProgressBar(false) }
                    .subscribe({ policyResponseData: PolicyDetailResponseDataModel ->
                        this.handlePolicyDetailResponse(
                            policyResponseData
                        )
                    }, { showAPIError() })
            },
                errorCallback = {
                    showAPIError()
                })
        }
    }

    fun handlePolicyDetailResponse(data: PolicyDetailResponseDataModel) {
        if (view == null || processInvalidResponse(responseDataToViewMapper.map(data))) {
            showAPIError()
            return
        }
        val bankingResponse = responseDataToViewMapper.map(data)
        parseFinanceResponse(bankingResponse)
    }

    private fun parseFinanceResponse(policyDetailResponse: PolicyDetailResponseViewModel?) {
        view?.setBankingDetailData(policyDetailResponse?.policyInquiry?.run {
            this[0].policy?.policySection?.policyBilling?.bankAccountList?.run {
                this[0]
            }
        })
    }

    fun showViewProgressBar(b: Boolean) {
        view?.showProgressBar(b)
    }

    fun showAPIError() {
        showViewProgressBar(false)
        view?.showAPIError()
    }

    fun editBankingDetails(
        policyNumber: String,
        selectedBankingDetail: BankAccountViewModel?,
        accountViewModel: AccountViewModel,
        productName: String?,
        isPolicyPL: Boolean
    ) {
        mNavigationRouter.navigateTo(
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_EDIT_BANKING_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAP_POLICY_NUMBER, policyNumber)
                .withParam(
                    InsuranceConstants.ParamKeys.PARAM_EDIT_BANKING_DETAILS, selectedBankingDetail
                ).withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
                .withParam(
                    InsuranceConstants.ParamKeys.PARAM_VVAP_RISK_SERIAL_NUMBER, mRiskSerialNumber
                ).withParam(
                    InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME,
                    productName ?: StringUtils.EMPTY_STRING
                ).withParam(InsuranceConstants.ParamKeys.PARAM_IS_PL_FLOW, isPolicyPL)
        )
    }

    fun sendEventWithProduct(eventName: String?, subProduct: String?, productType: String?) {
        val cdata = HashMap<String, Any>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY, productType
        )
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        adobeContextData.setProductAccount(productType)
        adobeContextData.setSubProduct(subProduct)
        mAnalytics.sendEventActionWithMap(eventName, cdata)
    }

    fun navigateToPolicyClaims() {
        mNavigationRouter.navigateTo(
            NavigationTarget.to(NavigationTarget.INSURANCE_POLICY_CLAIMS_SCREEN)
                .withIntentFlagClearTopSingleTop(true)
        )
        view?.close()
    }
}
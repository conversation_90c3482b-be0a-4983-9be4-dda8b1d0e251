/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.core.dashboard;

import android.util.SparseArray;

import za.co.nedbank.core.R;

public enum HomeWidget {
    BORROW(R.string.home_widget_offers_for_you, R.drawable.ic_offer_for_you_wrapper, HomeWidget.ACTION_BORROW, true, R.color.dark_grey, R.drawable.widget_square, true),
    APPLY(R.string.home_widget_apply, R.drawable.ic_apply_wrapper, HomeWidget.ACTION_APPLY, true, R.color.dark_grey, R.drawable.widget_square, true),
    INSURANCE(R.string.home_widget_insure, R.drawable.ic_insur_wrapper, HomeWidget.ACTION_INSURE, true, R.color.dark_grey, R.drawable.widget_square, false),
    MERCHANT_SERVICES(R.string.merchant_widget_insure, R.drawable.merchant, HomeWidget.ACTION_MERCHANT, true, R.color.dark_grey, R.drawable.widget_square, false),
    DISC_RENEWAL(R.string.disc_renewal_widget, R.drawable.ic_vehicle, HomeWidget.ACTION_DISC_RENEWAL, true, R.color.dark_grey, R.drawable.widget_square, false),
    SHOP(R.string.shop_widget, R.drawable.ic_widget_shop_kart_, HomeWidget.ACTION_SHOP, true, R.color.dark_grey, R.drawable.widget_square, true),
    PAYSHAP_REQUEST(R.string.home_widget_payshap_request, R.drawable.ic_wrapper_shap_id, HomeWidget.ACTION_PAYSHAP_REQUEST, true, R.color.dark_grey, R.drawable.widget_square, false),
    COVID_19(R.string.home_widget_latest, R.drawable.ic_icon_latest_active_wrapper, HomeWidget.ACTION_LATEST, true, R.color.dark_grey, R.drawable.widget_square, false),
    QUICK_PAY(R.string.home_widget_quick_pay, R.drawable.ic_quick_pay_wrapper, HomeWidget.ACTION_QUICK_PAY, true, R.color.dark_grey, R.drawable.widget_square, false),
    GET_CASH(R.string.home_widget_get_cash, R.drawable.ic_withdraw_money_wrapper, HomeWidget.ACTION_GET_CASH, false, R.color.dark_grey, R.drawable.widget_square, false),
    HOME_LOANS(R.string.home_widget_home_loans, R.drawable.ic_home_loan_wrapper, HomeWidget.ACTION_HOME_LOAN, true, R.color.dark_grey, R.drawable.widget_square, false),
    STATEMENTS(R.string.home_widget_statements, R.drawable.ic_statement_wrapper, HomeWidget.ACTION_STATEMENTS, true, R.color.dark_grey, R.drawable.widget_square, false),
    BUY_PREPAID(R.string.home_widget_buy_prepaid, R.drawable.ic_buy_prepaid_wrapper, HomeWidget.ACTION_BUY_PREPAID, false, R.color.dark_grey, R.drawable.widget_square, false),
    YOUR_BANKER(R.string.home_widget_your_banker, R.drawable.ic_your_banker_wrapper, HomeWidget.ACTION_YOUR_BANKER, false, R.color.dark_grey, R.drawable.widget_square, false),
    BUY_ELECTRICITY(R.string.home_widget_buy_electricity, R.drawable.ic_buy_electricity_wrapper, HomeWidget.ACTION_BUY_ELECTRICITY, false, R.color.dark_grey, R.drawable.widget_square, false),
    REPORT_FRAUD(R.string.home_widget_report_fraud, R.drawable.ic_report_fraud_wrapper, HomeWidget.ACTION_REPORT_FRAUD, true, R.color.dark_grey, R.drawable.widget_square, false),
    PAY_ME(R.string.home_widget_pay_me, R.drawable.ic_pay_me_wrapper, HomeWidget.ACTION_PAY_ME, false, R.color.dark_grey, R.drawable.widget_square, false);

    private int widgetName;
    private int widgetResource;
    private int actionId;
    private int textColor;
    private int bgDrawable;
    private boolean isWidgetShow;
    private boolean hasNotificationIcon;

    HomeWidget(int widgetName, int widgetResource, int actionId, boolean isWidgetShow, int textColor, int bgDrawable, boolean hasNotificationIcon) {
        this.widgetName = widgetName;
        this.widgetResource = widgetResource;
        this.actionId = actionId;
        this.isWidgetShow = isWidgetShow;
        this.hasNotificationIcon = hasNotificationIcon;
        this.textColor = textColor;
        this.bgDrawable = bgDrawable;
    }


    private static final SparseArray<HomeWidget> CATEGORY_MAP = new SparseArray<>();

    static {
        for (HomeWidget category : HomeWidget.values()) {
            CATEGORY_MAP.put(category.actionId, category);
        }
    }

    public static HomeWidget fromValue(final int value) {
        return CATEGORY_MAP.get(value, PAY_ME);
    }

    public int getWidgetName() {
        return widgetName;
    }

    public int getWidgetResource() {
        return widgetResource;
    }

    public int getActionId() {
        return actionId;
    }

    public boolean isWidgetShow() {
        return isWidgetShow;
    }

    public void setWidgetShow(boolean widgetShow) {
        isWidgetShow = widgetShow;
    }

    public int getTextColor() {
        return textColor;
    }

    public int getBgColor() {
        return bgDrawable;
    }

    public static final int ACTION_QUICK_PAY = 1;
    public static final int ACTION_PAY_ME = 2;
    public static final int ACTION_REPORT_FRAUD = 3;
    public static final int ACTION_BUY_PREPAID = 4;
    public static final int ACTION_YOUR_BANKER = 5;
    public static final int ACTION_BUY_ELECTRICITY = 6;
    public static final int ACTION_LATEST = 7;
    public static final int ACTION_BORROW = 8;
    public static final int ACTION_HOME_LOAN = 9;
    public static final int ACTION_GET_CASH = 10;
    public static final int ACTION_STATEMENTS = 12;
    public static final int ACTION_CREDIT_HEALTH = 11;
    public static final int ACTION_APPLY = 13;
    public static final int ACTION_SHAPID = 14;
    public static final int ACTION_INSURE = 15;

    public static final int ACTION_MERCHANT = 16;

    public static final int ACTION_SHOP = 17;
    public static final int ACTION_DISC_RENEWAL = 18;
    public static final int ACTION_PAYSHAP_REQUEST = 19;




}

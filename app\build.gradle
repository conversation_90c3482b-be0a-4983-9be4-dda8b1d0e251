apply plugin: 'com.android.application'
apply plugin: "com.hiya.jacoco-android"
apply plugin: 'com.google.firebase.crashlytics'
apply from: '../jacoco.gradle'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.huawei.agconnect'
apply plugin: 'org.jetbrains.kotlin.android'

def buildType
static def getBuildNumber() {
    return System.getenv("CIRCLE_BUILD_NUM") ?: "0"
}

jacocoAndroidUnitTestReport {
    // this exclude is just for demonstration. jacoco disables Java classes implicitly
    excludes += ['**/AutoValue_*.*', '**/*JavascriptBridge.class']
    csv.enabled true
    html.enabled true
    xml.enabled false
}

android {
    compileSdk rootProject.ext.compileSdkVersion

    signingConfigs {
        debug {
            storeFile file("debug.keystore")
            storePassword "nedbank-debug"
            keyAlias "debug-key"
            keyPassword "nedbank-debug-key"
        }
        release {
            storeFile file("release.keystore")
            storePassword project.properties['releaseStorePass']
            keyAlias "release-key"
            keyPassword project.properties['releaseKeyPass']
        }
    }
    // updating build type values "release", "qaRelease" etc. should also be updated
    // in za.co.nedbank.core.utils.DeviceUtils
    buildTypes {
        store {
            signingConfig signingConfigs.release
            buildType = 'store'
            minifyEnabled true
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testCoverageEnabled false
            versionNameSuffix '-prod'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_app_launcher",
                    appRoundIcon        : "@mipmap/ic_app_launcher",
                    HOCKEYAPP_APP_ID    : "@string/hockStore",
                    enableCrashReporting: "true"
            ]
            resValue "string", "GCMKEY", "\"$gcmKey\""
            matchingFallbacks = ['release']
            buildConfigField 'boolean', 'ENABLE_APPSFLYER', 'true'
            buildConfigField "String", "ADOBE_CONFIGURE_ID", '"6422e0f550a2/32bbd3446a1d/launch-d1f128b56233"'
            buildConfigField 'String', 'TAS_VENDOR_ID', "\"$tasVendorIdProd\""
            buildConfigField 'String', 'TAS_CLIENT_ID', "\"$tasClientIdProd\""
            buildConfigField 'String', 'TAS_CLIENT_KEY', "\"$tasClientKeyProd\""
            buildConfigField 'String', 'TAS_COMMENT', "\"$tasCommentProd\""
        }
        release {
            signingConfig signingConfigs.release
            buildType = 'prod'
            minifyEnabled true
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testCoverageEnabled false
            versionNameSuffix '-prod'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_app_launcher",
                    appRoundIcon        : "@mipmap/ic_app_launcher",
                    HOCKEYAPP_APP_ID    : "@string/hockStore",
                    enableCrashReporting: "true"
            ]
            resValue "string", "GCMKEY", "\"$gcmKey\""
            buildConfigField 'boolean', 'ENABLE_APPSFLYER', 'true'
            buildConfigField "String", "ADOBE_CONFIGURE_ID", '"6422e0f550a2/32bbd3446a1d/launch-d1f128b56233"'
            buildConfigField 'String', 'TAS_VENDOR_ID', "\"$tasVendorIdProd\""
            buildConfigField 'String', 'TAS_CLIENT_ID', "\"$tasClientIdProd\""
            buildConfigField 'String', 'TAS_CLIENT_KEY', "\"$tasClientKeyProd\""
            buildConfigField 'String', 'TAS_COMMENT', "\"$tasCommentProd\""
        }
        qa {
            signingConfig signingConfigs.debug
            buildType = "qa"
            minifyEnabled true
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testCoverageEnabled false
            versionNameSuffix '-qa'
            applicationIdSuffix '.qa'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_launcher_qa",
                    appRoundIcon        : "@mipmap/ic_launcher_qa",
                    HOCKEYAPP_APP_ID    : "539914e75ca74b0789d2bc108635cdb5",
                    enableCrashReporting: "true"
            ]
            resValue "string", "GCMKEY", "\"$gcmKey\""
            buildConfigField 'boolean', 'ENABLE_APPSFLYER', 'true'
            buildConfigField "String", "ADOBE_CONFIGURE_ID", '"6422e0f550a2/32bbd3446a1d/launch-df81d4b15d65-staging"'
        }
        ete {
            signingConfig signingConfigs.debug
            buildType = "qa"
            minifyEnabled true
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testCoverageEnabled true
            versionNameSuffix '-ete'
            applicationIdSuffix '.ete'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_launcher_debug",
                    appRoundIcon        : "@mipmap/ic_launcher_debug",
                    HOCKEYAPP_APP_ID    : "750063395e87429786325db81ac68372",
                    enableCrashReporting: "true"
            ]
            matchingFallbacks = ['qa']
            buildConfigField "String", "ADOBE_CONFIGURE_ID", '"6422e0f550a2/32bbd3446a1d/launch-df81d4b15d65-staging"'
            resValue "string", "GCMKEY", "\"$gcmKey\""
        }
        qaRelease {
            signingConfig signingConfigs.debug
            buildType = "qaRelease"
            minifyEnabled true
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testCoverageEnabled false
            versionNameSuffix '-qa-release'
            applicationIdSuffix '.qaRelease'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_launcher_qa_release",
                    appRoundIcon        : "@mipmap/ic_launcher_qa_release",
                    HOCKEYAPP_APP_ID    : "208d1e02ba23411aba147fbab336ea81",
                    enableCrashReporting: "true"
            ]
            buildConfigField 'boolean', 'ENABLE_APPSFLYER', 'true'
            buildConfigField "String", "ADOBE_CONFIGURE_ID", '"6422e0f550a2/32bbd3446a1d/launch-df81d4b15d65-staging"'
            resValue "string", "GCMKEY", "\"$gcmKey\""
        }
        debug {
            signingConfig signingConfigs.debug
            buildType = "dev"
            testCoverageEnabled false
            debuggable true
            versionNameSuffix '-dev'
            applicationIdSuffix '.debug'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_launcher_debug",
                    appRoundIcon        : "@mipmap/ic_launcher_debug",
                    HOCKEYAPP_APP_ID    : "539914e75ca74b0789d2bc108635cdb5",
                    enableCrashReporting: "false"
            ]
            buildConfigField "String", "ADOBE_CONFIGURE_ID", '"6422e0f550a2/32bbd3446a1d/launch-e0dbedd2865e-development"'
            resValue "string", "GCMKEY", "\"$gcmKey\""
        }
        mock {
            initWith(buildTypes.debug)
            buildType = "mock"
            testCoverageEnabled false
            debuggable true
            versionNameSuffix '-mock'
            applicationIdSuffix '.mock'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_launcher_debug",
                    appRoundIcon        : "@mipmap/ic_launcher_debug",
                    HOCKEYAPP_APP_ID    : "c87f88d15a714d6e9beda4b52d38f56a",
                    enableCrashReporting: "true"
            ]
            resValue "string", "GCMKEY", "\"$gcmKey\""
        }
        squad {
            signingConfig signingConfigs.debug
            buildType = "qa"
            minifyEnabled true
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testCoverageEnabled true
            versionNameSuffix project.properties['versionNameSuffix']
            applicationIdSuffix '.squad'
            manifestPlaceholders = [
                    appIcon             : "@mipmap/ic_launcher_squad",
                    appRoundIcon        : "@mipmap/ic_launcher_squad",
                    HOCKEYAPP_APP_ID    : "c87f88d15a714d6e9beda4b52d38f56a",
                    enableCrashReporting: "true"
            ]
            resValue "string", "GCMKEY", "\"$gcmKey\""
            buildConfigField 'boolean', 'ENABLE_APPSFLYER', 'true'
            matchingFallbacks = ['qa']
            buildConfigField "String", "ADOBE_CONFIGURE_ID", '"6422e0f550a2/32bbd3446a1d/launch-e0dbedd2865e-development"'
        }
    }

    dataBinding {
        enabled = true
    }

    defaultConfig {
        applicationId "za.co.nedbank"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName + '-' + getBuildNumber()
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables.useSupportLibrary = true
        multiDexEnabled true
        resConfigs "en", "US"
        buildConfigField 'boolean', 'ENABLE_APPSFLYER', 'true'
        buildConfigField 'String', 'TAS_VENDOR_ID', "\"$tasVendorId\""
        buildConfigField 'String', 'TAS_CLIENT_ID', "\"$tasClientId\""
        buildConfigField 'String', 'TAS_CLIENT_KEY', "\"$tasClientKey\""
        buildConfigField 'String', 'TAS_COMMENT', "\"$tasComment\""
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    packagingOptions {
        jniLibs {
            pickFirsts += ['lib/x86_64/libPhoenixAndroid.so', 'lib/armeabi/libPhoenixAndroid.so', 'lib/x86/libPhoenixAndroid.so', 'lib/armeabi-v7a/libPhoenixAndroid.so', 'lib/arm64-v8a/libPhoenixAndroid.so', 'lib/x86_64/libZoomDummy.so', 'lib/armeabi/libZoomDummy.so', 'lib/x86/libZoomDummy.so', 'lib/armeabi-v7a/libZoomDummy.so', 'lib/arm64-v8a/libZoomDummy.so']
        }
        resources {
            excludes += ['META-INF/rxjava.properties', 'lib/getLibs.ps1', 'lib/getLibs.sh', 'lib/gson-2.2.2.jar', 'META-INF/proguard/androidx-annotations.pro']
        }
    }
    testOptions {
        unitTests.returnDefaultValues = true
    }
    sourceSets {
        main { assets.srcDirs = ['src/main/assets', 'src/prod/assets/'] }
        qa { assets.srcDirs = ['src/qa/assets', 'src/qa/assets/'] }
        qaRelease { assets.srcDirs = ['src/qaRelease/assets', 'src/qaRelease/assets/'] }
    }
    lint {
        abortOnError false
        lintConfig file('lint.xml')
    }

    configurations {
        configureEach {
            exclude group: 'com.google.guava', module: 'listenablefuture'
            exclude group: 'com.google.guava', module: 'jetified-guava'
            exclude group: 'com.google.guava', module: 'jetified-listenablefuture'
            exclude group: 'com.google.errorprone', module: 'error_prone_annotations'
        }
    }
    buildFeatures {
        viewBinding = true
    }
}
tasks.withType(JavaCompile).configureEach{
    options.fork = true
    options.forkOptions.jvmArgs +=[
            '--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED',
            '--add-exports=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED',
            '--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED',
            '--add-exports=java.base/sun.nio.ch=ALL-UNNAMED',
            '--add-exports=java.base/java.security=ALL-UNNAMED',
            '--add-exports=java.base/java.io=ALL-UNNAMED',
            '--add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED']
}
dependencies {
    implementation project(':core')
    implementation project(':payment')
    implementation project(':profile')
    implementation project(':services')
    implementation project(':loans')
    implementation project(':enroll_v2')
    implementation project(':booking')
    implementation project(':kids-banking')
    implementation project(':document-upload')
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation fileTree(include: ['*.jar'], dir: '../core/libs')

    implementation "androidx.startup:startup-runtime:$rootProject.startRunTimeVersion"
    androidTestImplementation("androidx.test.espresso:espresso-core:$rootProject.espressoVersion", {
        exclude group: 'com.android.support', module: 'support-annotations'
        exclude group: 'com.google.code.findbugs', module: 'jsr305'
    })
    testImplementation "junit:junit:$rootProject.junitVersion"
    testImplementation "org.mockito:mockito-core:$rootProject.mockitoVersion"
    debugImplementation "com.squareup.leakcanary:leakcanary-android:$rootProject.leakCanaryVersion"
    mockImplementation "com.squareup.leakcanary:leakcanary-android:$rootProject.leakCanaryVersion"
    annotationProcessor "com.google.dagger:dagger-compiler:$rootProject.daggerVersion"
    implementation "com.ogaclejapan.arclayout:library:$rootProject.arcLayoutVersion@aar"
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation('com.huawei.hms:ml-computer-voice-asr:3.9.0.300',
            { exclude group:'com.google.code.gson'}
    )
    implementation "org.greenrobot:eventbus:$rootProject.eventbusVersion"
    implementation "com.google.firebase:firebase-analytics:$rootProject.firebaseAnalyticsVersion"
    implementation "com.google.firebase:firebase-crashlytics:$rootProject.firbaseCrashlyticsVersion"
    implementation "com.google.android.gms:play-services-tapandpay:$rootProject.tapAndPayVersion"
    implementation "me.leolin:ShortcutBadger:1.1.22@aar"
    if (project.hasProperty('kapt')) {
        kapt 'javax.xml.bind:jaxb-api:2.3.1'
        kapt 'com.sun.xml.bind:jaxb-core:2.3.0.1'
        kapt 'com.sun.xml.bind:jaxb-impl:2.3.2'
    }
    annotationProcessor 'javax.xml.bind:jaxb-api:2.3.1'
    annotationProcessor 'com.sun.xml.bind:jaxb-core:2.3.0.1'
    annotationProcessor 'com.sun.xml.bind:jaxb-impl:2.3.2'
}

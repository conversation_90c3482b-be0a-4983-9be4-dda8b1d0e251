/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.core.navigation;

import android.os.Bundle;
import android.os.Parcelable;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NavigationTarget {

    public static final String RRB_CREDIT_DETAILS_ACTIVITY = "rrb_credit_details_activity";
    public static final String CREDIT_DETAILS_ACTIVITY = "credit_details_activity";

    public static final String OCCUPATION_LIST_DATA = "occupation_data";
    public static final String SUBURB_AND_CITY_SEARCH_DATA = "suburb_list_data";

    public static final String EXPIRY_DATE = "expiry_date";
    public static final String PARAM_EXTRA_INVESTMENT_TERM = "param_extra_investment_term";
    public static final String PARAM_EXTRA_INVESTMENT_TERM_REMAINING = "param_extra_investment_term_remaining";

    public static final String KEY_INVESTMENT_ONLINE_MANAGE_INVEST_ACCOUNT_IS_NOTICE = "key.investmentonline.manage.item.account.isnotice";

    public static final String KEY_INVESTMENT_ONLINE_PRODUCT_TYPE = "key.product.type.of.investment";

    public static final String IS_ACCRUAL_DATE_AFTER = "is_Accrual_Date_After";

    public static final String AVAILABLE_BALANCE = "available_balance";
    public static final String RESERVED_FOR_RELEASE_BALANCE = "reserved_For_Release_balance";

    public static final String CURRENT_BALANCE = "current_balance";
    public static final String INVONLINE_PLACE_NOTICE = "nonfica_compliant_for_now";
    public static final String INVONLINE_DELETE_NOTICE = "nonfica_compliant_for_now";

    public static final String HOW_MUCH_WOULD_YOU_LIKE_TO_WITHDRAW = "key_how_much_would_like_to_withdraw";

    public static final String WHEN_WOULD_YOU_LIKE_TO_RECEIVE_THIS_AMOUNT = "key_withdrawal_date";

    public static final String BRANCH_OPENING_HOURS_ACTIVITY = "branch_opening_hours_activity";

    public static final String FICA_DHA_LEAD_CALL_BACK_REQUEST = "fica_dha_lead_call_back_request";

    public static final String KEY_ERROR_TYPE = "error_type";

    public static final String KEY_MAINTENANCE_TYPE = "maintenance_type";

    public static final String KEY_SURVEY_DATA = "survey_data";
    public static final String KEY_AVO_PROD_URL = "avo_product_url";
    public static final String KEY_AVO_TEMPLATE = "avo_template";

    public static final String SHAP_ID_MANAGEMENT = "shap_id_management";

    public static final String SHAP_ID_DETAILS = "shap_id_details";

    public static final String MAKE_PAYSHAP_REQUEST="make_payshap_request";

    public static final String REGISTER_SHAPE_ID = "register_shap_id";

    public static final String RPP_REGISTER_SUCCESS = "rpp_register_success";

    public static final String SHAP_ID_EDIT = "shap_id_edit";
    public static final String RETRIEVE_SHAP_ID_FAILURE = "retrieve_shap_id_failure";

    public static final String SHAP_ID_LINKED_ACCOUNTS = "shap_id_linked_accounts";

    public static final String ACCOUNT_DETAILS_CORE = "account_details";

    public static final String PARAM_IS_COMING_FROM_ROOTED_FLOW = "param_is_coming_from_rooted_flow";

    public static final String PARAM_IS_ATM_VISIBLE = "param_atm_and_branch_screen";

    public static final String PARAM_FOREX_BRANCH_TAB_SELECT = "param_forex_branch_tab_select";

    public static final String PARAM_NEDBANK_EXISTING = "param_nedbank_existing";

    public static final String PARAM_NEW_USERNAME = "param_new_username";

    public static final String PARAM_COUNTRY_NAME = "country_name";

    public static final String PARAM_PAYMENT_AMOUNT_EXCEED_INFO = "param_payment_amount_exceed_info";

    public static final String PARAM_PAYMENT_AMOUNT_EXCEED_REASON = "param_payment_amount_exceed_reason";

    public static final String PARAM_PAYMENT_AMOUNT_EXCEED_AMOUNT = "param_payment_amount_exceed_amount";

    public static final String PARAM_COUNTRY_IMAGE_NAME = "country_image_name";

    public static final String PARAM_EXTRA_PAYMENT_REFERENCE_NUMBER = "payment_ref_number";

    public static final String PARAM_EXTRA_PAYMENT_QUOTE_AMOUNT = "param_extra_payment_quote_amount";

    public static final String PARAM_EXTRA_PAYMENT_QUOTE_EXCHANGERATE = "param_extra_payment_quote_exchangerate";

    public static final String PARAM_EXTRA_PAYMENT_DETAIL = "payment_detail";

    public static final String PARAM_EXTRA_PAYMENT_PAY_TO_SELF_BOP_FLOW = "param_extra_payment_pay_to_self_bop_flow";

    public static final String PARAM_EXTRA_WHICH_IP_FLOW = "param_extra_which_ip_flow";

    public static final String PARAM_EXTRA_VALIDATE_MTCN_REQUEST_MODEL = "param_extra_validate_mtcn_request_model";

    public static final String PARAM_EXTRA_IS_FOOPBUS_AVAILABLE = "param_extra_is_foopbus_available";

    public static final String PARAM_EXTRA_IS_CMAZBUS_AVAILABLE = "param_extra_is_cmazbus_available";

    public static final String PARAM_EXTRA_IS_FOOPSELF_AVAILABLE = "param_extra_is_foopself_available";

    public static final String PARAM_EXTRA_SHOW_PAY_TO_BUSINESS = "param_extra_show_pay_to_business";

    public static final String PARAM_EXTRA_SHOW_ECOBANK_TILE = "param_extra_show_ecobank_tile";

    public static final String PARAM_EXTRA_SHOW_WESTERN_UNION_TILE = "param_extra_show_western_union_tile";

    public static final String PARAM_EXTRA_SHOW_INTERNATIONAL_ACCOUNT_TILE = "param_extra_show_international_account_tile";

    public static final String PARAM_EXTRA_DOCUMENT_CODE = "param_extra_document_code";

    public static final String PARAM_EXTRA_DOCUMENT_NAME = "param_extra_document_name";

    public static final String PARAM_EXTRA_IS_OTT_FLOW = "param_extra_is_ott_flow";

    public static final String PARAM_EXTRA_RECIPIENT_UPDATED_STATUS = "param_extra_recipient_updated_status";

    public static final String PARAM_EXTRA_IS_ECOBANK_FLOW = "param_extra_is_ecobank_flow";

    public static final String PARAM_EXTRA_IS_WESTERN_UNION_FLOW = "param_extra_is_western_union_flow";

    public static final String PARAM_EXTRA_IS_EXCEED_FCA_FINSURV_FLOW = "param_extra_is_exceed_fca_finsurv_flow";

    public static final String PARAM_EXTRA_IS_CMA_FLOW = "param_extra_is_cma_flow";

    public static final String PARAM_EXTRA_SELECTED_ACCOUNT_DETAIL = "param_extra_selected_account_detail";

    public static final String PARAM_EXTRA_PAYMENT_CREATE_RESPONSE = "param_extra_payment_create_response";

    public static final String PARAM_EXTRA_DARK_HOUR_TRANSACTION_LIMIT = "dark_hour_transaction_limit";

    public static final String PARAM_EXTRA_PAYMENT_SENDER_NAME = "payment_sender_name";

    public static final String PARAM_EXTRA_PAYMENT_AMOUNT = "payment_amount";

    public static final String PARAM_EXTRA_PAYMENT_BOP_CATEGORY = "bop_category";

    public static final String PARAM_EXTRA_PAYMENT_BOP_SUB_CATEGORY = "bop_sub_category";

    public static final String PARAM_EXTRA_LEARN_INFO_ISUCN = "param_extra_learn_info_isucn";

    public static final String PARAM_EXTRA_LEARN_MORE_HEADER = "param_extra_learn_more_header";

    public static final String PARAM_EXTRA_LEARN_MORE_DESCRIPTION = "param_extra_learn_more_description";

    public static final String PARAM_EXTRA_LEARN_MORE_IS_WU_RECEIVE = "param_extra_learn_more_is_wu_receive";

    public static final String PARAM_EXTRA_LEARN_MORE_VIEW_SAMPLE_URL = "param_extra_learn_more_view_sample_url";

    public static final String PARAM_EXTRA_DOC_UPLOAD_IMAGE_PATH = "param_extra_doc_upload_image_path";

    public static final String PARAM_EXTRA_PAYMENT_BOP_CATEGORY_DESC = "param_extra_payment_bop_category_desc";

    public static final String PARAM_EXTRA_PAYMENT_POST_SETTLE_DOC_UPLOAD_FLOW = "param_extra_payment_post_settle_doc_upload_flow";

    public static final String PARAM_EXTRA_ADD_OTT_RECIPIENT_TYPE = "param_extra_add_ott_recipient_type";

    public static final String PARAM_EXTRA_TRANSACTION_ITEM = "transaction_item";

    public static final String PARAM_EXTRA_IS_RECEIVE_ITT_TOGGLE_ITEM_ENABLE = "param_extra_is_receive_itt_toggle_item_enable";

    public static final String PARAM_EXTRA_IS_RECEIVE_WU_TOGGLE_ITEM_ENABLE = "param_extra_is_receive_wu_toggle_item_enable";

    public static final String PARAM_EXTRA_TRANSACTION_SEARCHED_TEXT = "transaction_searched_text";

    public static final String PARAM_EXTRA_SHOW_TRANSACTION_HISTORY = "show_transaction_history";

    public static final String HOME = "home";
    public static final String FICA_VERIFY_ME = "fica_verify_me";
    public static final String FICA_SUCCESS = "fica_success";
    public static final String FICA_ERROR = "fica_error";

    public static final String TAX_CERTIFICATE_ACCOUNTS = "tax_certificate_accounts";

    public static final String REINVEST_FIAS = "reinvest_fias_disclaimer";

    public static final String SHARE_ACC_INFO = "share_acc_info";

    public static final String PARAM_BIRTH_DATE = "param_birth_date";

    public static final String MOVING_TO_NEDBANK_ID = "moving_to_nedbank_id";

    public static final String LETS_CREATE_NEDBANK_ID = "lets_create_nedbank_id";

    public static final String GO_TO_DASHBOARD = "go_to_dashboard";

    public static final String CATEGORY = "category";

    public static final String TOP_UP_LANDING = "topup_landing";

    public static final String LOAD_TRIP = "load_trip";

    public static final String LOAD_TRIP_SUCCESS = "load_trip_success";

    public static final String TRIP_ERROR = "trip_error";

    public static final String SHOW_COUNTRIES = "show_countries";

    public static final String PAY = "pay";

    public static final String RPP_LANDING = "rpp_landing";

    public static final String BILL_PAYMENTS_TUTORIAL = "bill_payments_tutorial";

    public static final String BILL_PAYMENT_TYPE = "bill_payment_type";

    public static final String BILL_PAYMENTS_START = "bill_payments_start";

    public static final String BILL_PAYMENTS_OPTION = "bill_payments_options";

    public static final String BILL_PAYMENTS_OPTION_NAME = "bill_payments_option_name";

    public static final String BILL_PAYMENTS_NO_AMOUNT_DUE = "bill_payments_no_amount_due";

    public static final String BILL_PAYMENTS_NO_CARD = "bill_payments_no_card";

    public static final String BILL_PAYMENTS_ALERT = "bill_payments_alert";

    public static final String INTERNATIONAL_PAYMENT_OPTIONS = "international_payment_options";
    public static final String FAMILY_BANKING_VIEW = "family_banking_view";

    public static final String INTERNATIONAL_PAYMENT_INWARD_PAYMENT_DETAIL = "international_payment_inward_payment_detail";

    public static final String INTERNATIONAL_PAYMENT_RETURN_FUND_SUCCESS = "international_payment_return_fund_success";

    public static final String INTERNATIONAL_PAYMENT_RETURN_FUNDS = "international_payment_return_funds";

    public static final String INTERNATIONAL_PAYMENT_BOB_CATEGORY = "international_payment_bob_category";

    public static final String INTERNATIONAL_PAYMENT_DOC_SAVE_LATER = "international_payment_doc_save_later";

    public static final String INTERNATIONAL_PAYMENT_BOB_CATEGORY_DETAIL = "international_payment_bob_category_detail";

    public static final String INTERNATIONAL_PAYMENT_CLIENT_QUOTATION = "international_payment_client_quotation";

    public static final String INTERNATIONAL_PAYMENT_PAY_AMOUNT = "international_payment_client_pay_amount";

    public static final String INTERNATIONAL_PAYMENT_ADDITIONAL_INFO_SCREEN = "international_payment_additional_info_screen";

    public static final String INTERNATIONAL_PAYMENT_DARK_HOUR_AMOUNT_EXCEEDED = "international_payment_dark_hour_amount_exceeded";

    public static final String INTERNATIONAL_PAYMENT_DECLINE_CLIENT_QUOTATION = "international_payment_decline_quotation";

    public static final String INTERNATIONAL_PAYMENT_INWARD_PROCESS = "international_payment_accept_quotation";

    public static final String INTERNATIONAL_PAYMENT_RECEIVE_PAYMENT = "international_payment_receive_payment";

    public static final String INTERNATIONAL_PAYMENT_HISTORY = "international_payment_history";

    public static final String INTERNATIONAL_PAYMENT_TRANSACTION_DETAIL = "international_payment_transaction_detail";

    public static final String INTERNATIONAL_PAYMENT_BOB_CATEGORY_ADDITIONAL_INFO = "international_payment_bob_category_additional_info";

    public static final String INTERNATIONAL_PAYMENT_INFO_SCREEN = "international_payment_info_screen";

    public static final String INTERNATIONAL_PAYMENT_ADVANCE_TRANSACTION_HISTORY_SEARCH = "international_payment_advance_transaction_history_search";

    public static final String INTERNATIONAL_PAYMENT_PAY = "international_payment_pay";

    public static final String INTERNATIONAL_PAYMENT_BANK_LIST = "international_payment_bank_list";

    public static final String INTERNATIONAL_PAYMENT_PAYMENT_METHOD_LIST = "international_payment_payment_method_list";

    public static final String INTERNATIONAL_PAYMENT_DYNAMIC_FIELDS_GENERIC_LIST = "international_payment_dynamic_fields_generic_list";

    public static final String INTERNATIONAL_PAYMENT_DYNAMIC_FIELDS_TITLE = "international_payment_dynamic_fields_title";

    public static final String INTERNATIONAL_PAYMENT_DYNAMIC_FIELDS_CODE = "international_payment_dynamic_fields_code";

    public static final String INTERNATIONAL_PAYMENT_CURRENCY_SELECTION = "international_payment_currency_selection";

    public static final String INTERNATIONAL_PAYMENT_LEARN_MORE_INFO = "international_payment_learn_more_info";

    public static final String INTERNATIONAL_PAYMENT_LEARN_MORE_WITH_BULLETS_INFO = "international_payment_learn_more_with_bullets_info";

    public static final String INTERNATIONAL_PAYMENT_LEARN_MORE_SAMPLE = "international_payment_learn_more_sample";

    public static final String INTERNATIONAL_PAYMENT_ACCOUNT_SELECTION = "international_payment_account_selection";

    public static final String INTERNATIONAL_PAYMENT_DOC_REQUIRED_INFO = "international_payment_doc_required_info";

    public static final String INTERNATIONAL_PAYMENT_DOC_ADDITIONAL_INFO = "international_payment_doc_additional_info";

    public static final String INTERNATIONAL_PAYMENT_DOC_UPLOAD_SCREEN = "international_payment_doc_upload_screen";

    public static final String INTERNATIONAL_PAYMENT_DOC_UPLOAD_PROGRESS_VIEW = "international_payment_doc_upload_progress_view";

    public static final String RECIPIENT_OPTION_SELECTION = "recipient_option_selection";

    public static final String ADD_INTERNATIONAL_RECIPIENT_OPTION_SCREEN = "add_international_recipient_option_screen";

    public static final String ADD_INTERNATIONAL_RECIPIENT_IND_BUSS_OPTION_SCREEN = "add_international_recipient_ind_buss_option_screen";

    public static final String ADD_INTERNATIONAL_RECIPIENT_DETAILS_SCREEN = "add_international_recipient_details_screen";

    public static final String INTERNATIONAL_RECIPIENT_DETAILS_SCREEN = "international_recipient_details_screen";

    public static final String INTERNATIONAL_PAYMENT_RECEIVE_OPTION_SCREEN = "international_payment_receive_option_screen";

    public static final String INTERNATIONAL_PAYMENT_RECEIVE_WESTERN_UNION_MTCN_SCREEN = "international_payment_receive_western_union_mtcn_screen";
    public static final String INTERNATIONAL_PAYMENT_VALIDATE_MTCN_PROGRESS_SCREEN = "international_payment_validate_mtcn_progress_screen";

    public static final String FCA_OPEN_ACC_INFO_SCREEN = "fca_open_acc_info_screen";
    public static final String FCA_OPEN_ACC_DETAIL_INFO_SCREEN = "fca_open_acc_detail_info_screen";

    public static final String FCA_ACC_SELECTION_SCREEN = "fca_acc_selection_screen";

    public static final String FCA_CURRENCY_SELECTION_SCREEN = "fca_currency_selection_screen";

    public static final String FCA_BUY_SELL_CURRENCY_SELECTION_SCREEN = "fca_buy_sell_currency_selection_screen";
    public static final String FCA_ENTER_AMOUNT_SCREEN = "fca_enter_amount_screen";

    public static final String FCA_QUOTATION_SCREEN = "fca_quotation_screen";

    public static final String FCA_SUCCESS_SCREEN = "fca_success_screen";

    public static final String FCA_ACCOUNTS_LIST_SCREEN = "fca_accounts_list_screen";

    public static final String FCA_CURRENCY_ACCOUNTS_DETAIL_SCREEN = "fca_currency_accounts_detail_screen";

    public static final String FCA_PENDING_TRANSACTION_SCREEN = "fca_pending_transaction_screen";
    public static final String FCA_SELECT_SDA_SCREEN = "fca_select_sda_screen";
    public static final String FCA_SDA_LEARN_MORE_SCREEN = "fca_sda_learn_more_screen";

    public static final String FCA_DOC_REQ_INFO_SCREEN = "fca_doc_req_info_screen";

    public static final String FCA_DOC_REQ_MORE_INFO = "fca_doc_req_more_info";

    public static final String FCA_ADVANCE_SEARCH = "fca_advance_search";

    public static final String FCA_STATEMENT_EMAIL_PREFERENCE = "fca_statement_email_preference";

    public static final String FOREX_APPLY_SETTING = "forex_apply_setting";

    public static final String FCA_STATEMENT_SUB_FEATURES_LIST = "fca_statement_sub_features_list";

    public static final String FCA_SELECT_STATEMENT_FORMAT_SCREEN = "fca_select_statement_format_screen";

    public static final String FCA_STATEMENT_FORMAT_SCREEN = "fca_statement_format_screen";

    public static final String FCA_DOC_ACCOUNT_LIST_SCREEN = "fca_doc_account_list_screen";

    public static final String FCA_TRANSACTION_DETAIL_SCREEN = "fca_transaction_detail_screen";

    public static final String FCA_CLOSED_ACCOUNTS_ACTIVITY= "fca_closed_accounts_activity";

    public static final String PARAM_EXTRA_WHICH_FCA_FLOW = "param_extra_which_fca_flow";

    public static final String PARAM_EXTRA_FCA_SELECTED_BOP = "param_extra_fca_selected_bop";
    public static final String PARAM_EXTRA_FCA_COMMON_OBJECT = "param_extra_fca_common_object";
    public static final String PARAM_EXTRA_FCA_ACCOUNT_CURRENCY_OBJECT = "param_extra_fca_account_currency_object";
    public static final String PARAM_EXTRA_FCA_ACCOUNT_LIST = "param_extra_fca_account_list";
    public static final String PARAM_EXTRA_FCA_ACCOUNT_NUM = "param_extra_fca_account_num";

    public static final String PARAM_EXTRA_FCA_CURRENCY_DESCRIPTION = "param_extra_fca_currency_description";

    public static final String PARAM_EXTRA_FCA_REFERENCE_NUMBER = "param_extra_fca_reference_number";

    public static final String PARAM_EXTRA_FCA_QUOTE_AMOUNT = "param_extra_fca_quote_amount";

    public static final String PARAM_EXTRA_FCA_ACC_CREATE_ACCEPT_FAIL = "param_extra_fca_acc_create_accept_fail";

    public static final String PARAM_EXTRA_FCA_DARK_HOUR_STATUS = "param_extra_fca_dark_hour_status";

    public static final String PARAM_EXTRA_FCA_SELECTED_ACCOUNT_NAME = "param_extra_fca_selected_account_name";

    public static final String PARAM_RECIPIENT_DETAIL_OBJ = "param_serialize_recipient_detail_object";

    public static final String PARAM_DYNAMIC_FIELDS_GENERIC_LIST = "param_dynamic_fields_generic_list";

    public static final String PARAM_DYNAMIC_FIELDS_LIST_SCREEN_TITLE = "param_dynamic_fields_list_screen_title";

    public static final String PARAM_GENERIC_SCREEN_TITLE = "PARAM_GENERIC_SCREEN_TITLE";

    public static final String PARAM_SHOW_ITT = "PARAM_SHOW_ITT";

    public static final String PARAM_COUNTRY_CODE = "param_country_code";

    public static final String PARAM_CHANNEL_CODE = "param_channel_code";

    public static final String PARAM_PRODUCT_CODE = "param_product_code";

    public static final String PARAM_CURRENCY_PAYMENT_METHOD_CODE = "param_currency_payment_method_code";

    public static final String PARAM_CURRENCY_RECIPIENT_TYPE_CODE = "param_currency_recipient_type_code";

    public static final String PARAM_PAYMENT_METHOD_CODE = "param_payment_method_code";

    public static final String PARAM_PAYMENT_METHOD_TITLE = "param_payment_method_title";

    public static final String PARAM_INTERNATIONAL_BANKING_SAVED_TAB_FLOW = "param_international_banking_saved_tab_flow";

    public static final String PARAM_INTERNATIONAL_BANKING_IS_DOC_REQ = "param_international_banking_is_doc_req";

    public static final String TRANSACTION_HISTORY = "transaction_history";

    public static final String PAY_LANDING = "pay_landing";

    public static final String PAY_DONE = "pay_done";

    public static final String RECENT_PAYMENT_HISTORY = "recent_payment_history";

    public static final String GOVT_PAYMENT = "govt_payment";

    public static final String PAY_AMOUNT = "pay_amount";

    public static final String PARAM_INTERNATIONAL_PAYMENT_INFO_SCREEN_OP = "param_international_payment_info_screen_op";

    public static final String GOVT_PAYMENT_TRANSACTION_DETAILS = "govt_payment_transaction_details";

    public static final String TRANSFER = "transfer";
    public static final String HL_SETTLEMENT_ERROR = "hl_settlement_error_activity";
    public static final String MOA_PRODUCT_DETAIL = "moa_product_detail";
    public static final String MOA_BASIC_INFORMATION = "moa_basic_information";

    public static final String ERROR_SCREEN_ACTIVITY = "error_screen_activity";

    public static final String BUY_LANDING = "buy_landing";

    public static final String DEBIT_ORDER_FRAGMENT = "debit_order_fragment";

    public static final String SHARE_ACCOUNT_INFO_ACTIVITY = "share_account_info_activity";

    public static final String ACCOUNT_STATEMENTS_ACTIVITY = "account_statements_activity";

    public static final String ADD_RECIPIENT = "add_recipient";

    public static final String RECIPIENT_DETAIL = "recipient_detail";

    public static final String EDIT_RECIPIENT = "edit_recipient";

    public static final String RECIPIENT_ID = "recipient_id";

    public static final String BUY = "buy";

    public static final String CONTACT_US = "contact_us";

    public static final String CONTACT_US_ITEM = "contact_us_item";

    public static final String GET_IN_TOUCH = "get_in_touch";

    public static final String ATM_AND_BRANCHES_PROFILE = "atm_and_branches_profile";

    public static final String LOGIN = "login";

    public static final String LOGIN_MORE = "login_more";

    public static final String IS_DEVICE_ROOTED_PARAM = "is_device_rooted";

    public static final String CARD_FOR_DASHBOARD = "card_for_dashboard";

    public static final String NEED_HELP_WITH_NEDBANK_ID = "need_help_with_nedbank_id_";

    public static final String SPLASH_SCREEN = "splash_screen";

    public static final String DEVELOPER_OPTION_ALERT = "developer_option_alert";

    public static final String TERMS_AND_CONDITIONS = "terms_and_conditions_sections";

    public static final String TERMS_AND_CONDITIONS_DETAILS = "terms_and_conditions_details";

    public static final String TERMS_AND_CONDITIONS_NOTICE_TYPE_PARAM = "terms_and_conditions_notice_type";

    public static final String TERMS_AND_CONDITIONS_NAME_PARAM = "terms_and_conditions_name";

    public static final String TERMS_AND_CONDITIONS_NEED_ACCEPTANCE_PARAM = "terms_and_conditions_need_acceptance_param";

    public static final String TERMS_AND_CONDITIONS_SCAN_DETAILS = "terms_and_conditions_scan_details";

    public static final String BRANCH_PROFILE_SELECT = "branch_profile_select";

    public static final String RESULT_BRANCH_ID = "branch_id";

    public static final String RESULT_BRANCH_NAME = "branch_name";

    public static final String RESULT_BRANCH_ADDRESS = "branch_address";

    public static final String RESULT_BRANCH_SUBURB = "branch_suburb";

    public static final String RESULT_BRANCH_TOWN = "branch_town";

    public static final String RESULT_BRANCH_PROVINCE = "branch_province";

    public static final String RESULT_BRANCH_POSTAL_CODE = "branch_postal_code";

    public static final String DATA_USAGE_WARNING = "data_usage_warning";

    public static final String TRIP_SELECT_CARD = "trip_select_card";

    public static final String USER_SELECTED_POCKETS = "USER_SELECTED_POCKETS";

    public static final String RESULT_DATA_USAGE_WARNING_ACCEPT = "data_warning_accept";

    public static final String SMA_ACCOUNT_DETAILS = "sma_account_details";
    public static final String IS_NEW_SERVICE_LOOGED = "service_logged_new";
    public static final String IS_DEBIT_ORDER_SUCCESS_DONE = "mfc_debit_order_done";

    public static final String MERCHANT_ACCOUNT_ID = "merchant_account_id";

    public static final String SMA_RETRY_OPERATION = "sma_retry_operation";

    public static final String ACCOUNT_MANAGEMENT = "account_management";
    public static final String PROFILE_MANAGEMENT = "profile_management";

    public static final String COMING_SOON = "coming_soon";

    public static final String SCAN_TO_PAY_FLOW_TYPE_PARAM = "scan_to_pay_flow_type";

    public static final String SCAN_TO_PAYMENT = "scan_to_payment";

    public static final String SCAN_PAYMENT_DONE_SCREEN = "scan_payment_done_screen";

    public static final String SCAN_TERM_AND_CONDITION_SCREEN = "scan_term_and_condition_activity";

    public static final String SCAN_TERM_AND_CONDITION_UPDATED_SCREEN = "scan_term_and_condition_updated_activity";

    public static final String SCAN_NOT_ENROLLED_SCREEN = "scan_not_enrolled_screen";

    public static final String NOCARDS_AVAILABLE = "no_cards_available";

    public static final String SCAN_PAY_CARDS_BLOCK = "scan_payment_cards_block";

    public static final String WORKING_HOURS = "working_hour";

    public static final String Travel_CardPocket_Trips_screen = "travel_cardpocket_trips_screen";

    public static final String TRAVEL_CARD_VISIT_BRANCH_SCREEN = "travel_card_visit_branch_screen";

    public static final String SCAN_TO_PAY_DETAIL = "scan_to_pay_detail";

    public static final String SCAN_TO_PAY_CAMERA_PERMISSION_PARAM = "scan_to_pay_camera_permission_param";

    public static final String QR_CODE_PARAM = "qr_code_string";

    public static final String MASTERPASS_PURCHASE_CODE_PARAM = "masterpass_purchase_code_string";

    public static final String MASTERPASS_RETURN_URL_PARAM = "masterpass_return_url_string";

    public static final String MASTERPASS_ISFROM_MOBI_PARAM = "masterpass_isfrom_mobi_string";

    public static final String APP_BLOCK = "app_block";

    public static final String SET_DEFAULT_ACCOUNT = "SET_DEFAULT_ACCOUNT";

    public static final String ERROR_MESSAGE_NO_ENTRY = "ERROR_MESSAGE_NO_ENTRY";
    public static final String MAINTENANCE_PAGE = "MAINTENANCE_PAGE";
    public static final String APP_FORCE_UPDATE_NEEDED = "app_update_needed";
    public static final String APP_SOFT_UPDATE_AVAILABLE = "app_update_available";
    public static final String SCAN2PAY_APP_UPDATE_NEEDED = "scan2pay_app_update_needed";
    public static final String APP_ROOTED = "app_rooted";
    public static final String APP_BLACK_LIST = "app_black_list";
    public static final String OS_OUTDATED = "os_outdated";
    public static final String CURRENCY_ACTIVITY = "currency_activity";
    public static final String TRAVEL_CARD_REVIEW_SELL_ACTIVITY = "travel_card_review_sell_activity";
    public static final String FROM_SECOND_LOGIN_SCREEN = "from_second_login_screen";
    public static final String IS_FROM_APP_SHORTCUT = "is_from_app_shortcut";
    public static final String IS_FROM_THIRD_PARTY_APP = "is_from_third_party_app";
    public static final String PARAM_RECEIVER_MOBILE_NUMBER = "param_receiver_mobile_number";
    public static final String PARAM_RECEIVER_DESCRIPTION = "param_receiver_description";
    public static final String PARAM_RECEIVER_ACCOUNT_NUMBER = "param_receiver_account_number";
    public static final String MONEY_REQUEST_SUCCESS = "param_money_request_success";
    public static final String IS_FROM_REPORT = "is_from_report";
    public static final String AVO_LIFESTYLE_SCREEN = "avo_lifestyle_activity";
    public static final String INSURANCE_EMERGENCY_SERVICES = "insurance_emergency_services";
    public static final String INSURANCE_POLICY_CLAIMS_SCREEN = "insurance_policy_claim_screen";

    public static final String SMA_MERCHANT_LANDING = "sma_merchant_landing";
    public static final String SHOP_DASHBOARD = "shop_dashboard";
    public static final String AVO_DEMO_SPLASH_SCREEN = "avo_demo_splash_screen";
    public static final String AVO_FAILURE_SCREEN = "avo_failure_screen";
    public static final String AVO_FAILURE_CLOSE = "avo_failure_close";
    public static final String SMA_DEVICE_ERROR_AND_HELP = "sma_device_error_and_help";
    public static final String SMA_SUCESS = "sma_sucess";
    public static final String SMA_BUSINESS_DETAILS = "sma_business_details";

    public static final String SMA_ACCOUNT_SELECTION = "sma_account_selection";
    public static final String SMA_STATEMENT_DETAILS = "sma_statement_details";
    public static final String SMA_TRANSACTION_DETAILS = "sma_transaction_details";
    public static final String SMA_TRANSACTION_HISTORY = "sma_transaction_history";
    public static final String SMA_SERVICE_REQUEST_DETAILS = "sma_service_request_details";
    public static final String SMA_POS_ERROR = "sma_pos_error";
    public static final String SMA_POS_ERROR_DETAIL = "sma_pos_error_detail";

    public static final String SMA_SERVICE_REQUEST_DETAIL = "sma_service_request_detail";

    public static final String SMA_QUERIES_SCREEN = "sma_queries_screen";
    public static final String SMA_NEW_REQUEST_SUCCESS_SCREEN = "sma_new_request_success_screen";
    public static final String SMA_NEW_SERVICE_SCREEN = "sma_new_service_screen";

    public static final String SMA_ACCOUNT_LISTING = "sma_account_listing";
    public static final String SMA_TECHNICAL_ERROR = "sma_technical_error";

    public static final String SMA_STATEMENT_ERROR = "sma_statement_error";

    public static final String SMA_MERCHANT_INTRODUCTION = "sma_merchant_introduction";
    public static final String AVO_MOVING_TO_AVO = "avo_moving_to_avo";
    public static final String INSURANCE_POLICY_LIST = "insurance_policy_list";
    public static final String INSURANCE_MANAGE_OR_SUBMIT_POLICY_LIST = "insurance_manage_submit_list";
    public static final String INSURANCE_DASHBOARD_SCREEN = "insurance_dashboard_policy_screen";
    public static final String INSURANCE_VVAP_PRODUCT_LIST_SCREEN = "insurance_vvap_product_list_screen";
    public static final String PARAM_PAYMENT_MODEL = "param_payment_model";
    public static final String MFC_SETTLEMENT_QUOTE = "mfc_settlement_quote";
    public static final String RECIPIENT_PHONE_NUMBER = "param_recipient_phone_number";
    public static final String PARAM_RECIPIENT_NAME = "param_recipient_name";
    public static final String VIEW_MONEY_REQUESTS = "param_view_money_requests";
    public static final String CHAT_SCREEN = "chat_screen";
    public static final String INITIATE_CHAT_SCREEN = "initiate_chat_screen";
    public static final String APP_DEEPLINK_MANAGER = "app_deeplink_manager";
    public static final String CORE_DEEPLINK_MANAGER = "CORE_DEEPLINK_MANAGER";
    public static final String DEEPLINK_ERROR = "DEEPLINK_ERROR";
    public static final String PARAM_NAME = "name";
    public static final String PARAM_EMAIL = "email";
    public static final String PARAM_EXTRA_FEATURE_NAME = "feature_name";
    public static final String DEEP_LINK_INTERMEDIATE_SCREEN = "deep_link_intermediate_activity";
    public static final String DEEP_LINK_PRODUCT_SHORT_DETAIL = "deep_link_product_details";
    public static final String DEEP_LINK_PRODUCT_FULL_DETAIL = "deep_link_product_full_detail";
    public static final String CROSS_BORDER_LANDING = "cross_border_landing";
    public static final String CROSS_BORDER_ACCOUNT_SELCTION = "cross_border_account_selection";
    public static final String CREATE_PASSWORD_WARNING_SCREEN = "create_password_warning_screen";
    public static final String IS_AMEX_SUPPORTED = "is_amex_supported";
    public static final String KEY_AUTH_REFERENCE = "auth_reference";
    public static final String KEY_LOGIN_FLOW_TYPE = "login_flow_type";
    public static final String KEY_USERNAME = "key_username";
    public static final String SCAN_PAY_AUTHENTICATION = "scan_pay_authentication";
    public static final String TERMS_AND_CONDITION_LOGIN = "terms_and_condition";
    public static final String SELECT_CARD = "select_card";
    public static final String CREDIT_HEALTH_FLOW = "credit_health_flow";
    public static final String ONCEOFF_PAYMENTS = "onceoff_payments";
    public static final String LOC_ERROR_SCREEN = "loc_error_screen";
    public static final String DISPLAY_SURVEY = "display_survey";
    public static final String PARAM_SETTLEMENT_AMOUNT = "settlement_amount";
    public static final String PARAM_ACCOUNT_NUMBER = "account_number";
    public static final String VIEW_MONEY_RESPONSE = "param_view_money_response";
    public static final String PARAM_RECEIVER_NAME = "param_receiver_name";
    public static final String BECOME_CLIENT = "become_client";
    public static final String FICA_SELECT_INTENT = "fica_select_intent";
    public static final String FICA_ACCOUNT_BENEFITS = "fica_account_benefits";
    public static final String FICA_EXISTING_CLIENT_CONTINUE = "fica_existing_client_continue";
    public static final String FICA_AVAILABLE_PRODUCT_SUGGESTION = "fica_available_product_suggestion";
    public static final String FICA_PENDING_IDENTITY = "fica_pending_identity";
    public static final String MOA_PRODUCT_LIST = "moa_product_list";
    public static final String MOA_TAX_OBLIGATIONS = "moa_tax_obligations";
    public static final String MOA_EMAIL_ADDRESS = "moa_email_address";
    public static final String FICA_USER_CALLBACK = "fica_user_callback";
    public static final String MOA_UPDATE_CUSTOMER_SALARY = "moa_update_customer_salary";
    public static final String MOA_UPDATE_CUSTOMER_SALARY_PREP = "moa_update_customer_salary_prep";
    public static final String PARAM_RECEIVER_ACCOUNT_TYPE = "param_receiver_account_type";
    public static final String PARAM_REQUESTED_AMOUNT = "param_requested_amount";
    public static final String MONEY_RESPONSE_REVIEW = "param_money_response_review";
    public static final String MONEY_RESPONSE_SUCCESS = "param_money_response_success";
    public static final String PARAM_PAYMENT_REQUEST_ID = "param_payment_request_id";
    public static final String PARAM_NOTIFICATION_MOBILE_NUMBER = "param_notification_mobile_number";
    public static final String PARAM_NOTIFICATION_EMAIL_ADDRESS = "param_notification_email_address";
    public static final String PARAM_REFERENCE_NUMBER = "param_reference_number";
    public static final String PARAM_PHONE_NO = "param_phone_no";
    public static final String PARAM_BIO_SESSION_TOKEN = "param_bio_session_token";
    public static final String PARAM_PROCESS_TYPE = "param_process_type";
    public static final String PARAM_LIMIT_TYPE = "param_limit_type";
    public static final String PARAM_VERIFICATION_INFO = "param_verification_info";
    public static final String PARAM_DATE_FROM = "param_date_from";
    public static final String PARAM_DATE_TO = "param_date_to";
    public static final String FEEDBACK_SUCCESS = "feedback_success";
    public static final String PARAM_QUOTATION = "param_quotation";
    public static final String PARAM_CURRENCIES = "param_currencies";
    public static final String PARAM_CURRENCY = "param_currency";
    public static final String PARAM_CUT_OFF_TIME = "param_cut_off_time";
    public static final String PARAM_EXCHANGE_AMOUNT = "param_exchange_amount";
    public static final String PARAM_YOUR_REF = "param_your_ref";
    public static final String PARAM_RECEIPT_EMAIL_ADDRESS = "param_receipt_email_address";
    public static final String PARAM_FROM_ACCOUNT = "param_from_account";
    public static final String PARAM_TRAVEL_ACCOUNT = "param_travel_account";
    public static final String PARAM_TRAVEL_ACCOUNT_HOLDER_NAME = "param_travel_account_holder_name";
    public static final String PARAM_TRAVEL_TO_ACCOUNT_NAME = "param_travel_to_account_name";
    public static final String PARAM_TRAVEL_ACCOUNT_ID = "param_travel_account_id";
    public static final String PARAM_TRAVEL_SELL_FLOW = "param_travel_sell_flow";
    public static final String PARAM_MOA_FLOW = "param_moa_flow";
    public static final String PARAM_TRAVEL_ACCOUNT_EMAIL_ID = "param_travel_account_email_id";
    public static final String PARAM_TRIP_INFO = "param_trip_info";
    public static final String PARAM_CURRENCY_SELECTED = "param_currency_selected";
    public static final String SELECTED_ITEM = "selected_item";
    public static final String SELECTED_INDEX = "selected_index";
    public static final String PARAM_TOP_UP_SUCCESS_INFO = "param_top_up_success_info";
    public static final String PARAM_ERROR_CODE = "param_error_code";
    public static final String PARAM_ERROR_MESSAGE = "param_error_message";
    public static final String PARAM_CELL_NO_CHANGE_FLOW = "param_is_cell_no_change_flow";
    public static final String PARAM_GOVT_PENDING_COUNT = "param_govt_pending_count";
    public static final String PARAM_GOTO_PROFILE = "param_goto_profile";
    public static final String PARAM_GOTO_PROFILE_SUCCESS = "param_goto_profile_SUCCESS";
    public static final String PARAM_FAILURE_TITLE = "param_failure_title";
    public static final String PARAM_FAILURE_DESCRIPTION = "param_failure_description";
    public static final String PARAM_FAILURE_BUTTON_TEXT = "param_failure_button_text";
    public static final String PARAM_FAILURE_TYPE = "param_failure_type";
    public static final String PARAM_FAILURE_ITEM_URL = "param_failure_item_url";
    public static final String PARAM_FAILURE_ITEM_TEMPLATE = "param_failure_item_template";

    public static final String ODD_RESTRICTION_ERROR = "odd_restriction_error";

    public static final String TARGET_ITA_FLOW_SCREEN = "target_ita_flow_screen";
    public static final String QRCODE_FLOW_PROCESSING = "qrcode_flow_processing";
    public static final String QR_LOGIN_RESULT = "qr_login_result";

    public static final String VIEW_BANKER = "view_banker_details";
    public static final String PARAM_BANKER_RETRY = "banker_retry";
    public static final String PARAM_BANKER_FIRSTNAME = "banker_first_name";
    public static final String PARAM_BANKER_LASTNAME = "banker_last_name";
    public static final String PARAM_IS_BABKER = "is_banker";
    public static final String PARAM_BANKER_EMAIL = "banker_email";
    public static final String PARAM_BANKER_PHONE = "banker_phone";
    public static final String PARAM_BANKER_IMAGE = "banker_image";
    public static final String PARAM_BANKER_DIVISION = "banker_divison";
    public static final String PARAM_SEC_OFFICER_CD = "secOfficerCd";
    public static final String PARAM_APPOINTMENT_LIST_VIEW_MODEL = "appointment_list_view_model";
    public static final String PARAM_BOOKING_ENTRY_TYPE = "booking_entry_type";
    public static final String PARAM_IS_APPOINTMENT_CREATED = "is_appointment_created";
    public static final String PARAM_BUSINESS_UNIT_TYPE = "business_unit_type";
    public static final String PARAM_POS_DETAIL_MODEL = "pos_detail_model";
    public static final String SMA_DETAILS_TYPE = "sma_details_type";
    public static final String PARAM_SERVICE_REQUEST_MODEL = "service_request_detail_model";
    public static final String PARAM_QUERY_MODEL = "pos_query_model";
    public static final String PARAM_SERVICE_INSTRUCT_NO = "service_instruct_no";

    public static final String PARAM_LIFESTYLE_CARD_IMAGE_ASSOCIATION = "lifestyle_card_image";
    public static final String PARAM_LIFESTYLE_CARD_NUMBER = "lifestyle_card_number";
    public static final String PARAM_LIFESTYLE_CARD_DESCRIPTION = "lifestyle_card_description";
    public static final String PARAM_LIFESTYLE_CARD_EXPIRY_DATE = "lifestyle_card_expiry_date";
    public static final String PARAM_LIFESTYLE_CARD_OWNER = "lifestyle_card_owner";
    public static final String NEW_PROFILE_DETAILS = "new_profile_details";

    public static final String JURISTIC_FINANCIAL_DETAILS = "juristic_financial_details";
    public static final String ADDITIONAL_CONTACT_DETAILS = "additional_contact_details";
    public static final String INVALID_TIN = "invalid_tin";
    public static final String NO_TIN_REASON = "no_tin_reason";
    public static final String ADD_NATIONALITY_TIN = "add_nationality_tin";
    public static final String CONFIRM_ODD_DETAILS = "confirm_odd_details";
    public static final String CONFIRM_FATCA_DETAILS = "confirm_fatca_details";
    public static final String FATCA_STEPPER_DETAILS = "fatca_stepper_details";
    public static final String FATCA_TNC_DETAILS = "fatca_tnc_details";
    public static final String FATCA_SUCCESS_SCREEN = "fatca_success_Screen";
    public static final String EDIT_PRIMARY_CELL_NUMBER = "edit_primary_cell_number";
    public static final String SELECT_CONTACT_NUMBER_TYPE = "select_contact_number_type";
    public static final String ADD_CONTACT_NUMBER = "add_contact_number";
    public static final String CONFIRM_EDIT_PRIMARY_CELL_NUMBER = "confirm_edit_primary_cell_number";
    public static final String SECURITY_ALERT_SCREEN = "security_alert_screen";
    public static final String VERIFY_IDENTITY = "verify_your_identity";
    public static final String CONSENT_SCREEN = "consent_screen";
    public static final String STOP_SCREEN = "stop_screen";
    public static final String FOREIGN_TAX_OBLIGATION = "foreign_tax_obligation";
    public static final String US_CITIZEN = "us_citizen";
    public static final String ERROR_SCREEN_PROFILE = "error_screen";
    public static final String PARAM_IS_FROM_GOTOBANK = "go_to_bank";
    public static final String PARAM_IS_FROM_TECHNICAL_ERROR = "techinical_error";
    public static final String PARAM_JURISTIC_CONTACT = "juristic_contact";
    public static final String PARAM_JURISTIC_CONTACT_MODE = "juristic_contact_mode";
    public static final String PARAM_IS_FROM_TIN_ERROR = "tin_error";
    public static final String PARAM_IS_FROM_TIN_API_ERROR = "tin_api_error";
    public static final String PARAM_TIN_ERROR_MESSAGE = "tin_error_message";
    public static final String PARAM_IS_FROM_MISSING_INFO = "missing_info";
    public static final String PARAM_IS_FROM_NATIONALITY_SCREEN = "nationality_screen";
    public static final String PARAM_IS_FROM_ODD_MISSING_INFO_SCREEN = "odd_missing_info_screen";
    public static final String PARAM_IS_FROM_FATCA_MISSING_INFO_SCREEN = "fatca_missing_info_screen";
    public static final String PARAM_LEAVING_SO_SOON = "leaving_so_soon";
    public static final String PARAM_PAGE_TYPE = "page_type";
    public static final String PARAM_PERSONAL_DETAILS_ERROR = "odd_personal_details_error";
    public static final String PARAM_SUCCESS_SCREEN = "odd_success_screen";
    public static final String PARAM_ODD_STATUS_VALUE = "odd_status_value";
    public static final String PARAM_ADDRESS_ERROR = "odd_address_error";
    public static final String PARAM_RESIDENCE_ERROR = "odd_residence_error";
    public static final String INVONLINE_DELETE_EARLY_RELEASE = "invonline_delete_early_release";
    public static final String  CONFIRMED_EARLY_WITHDRAWAL_ACTIVITY = "confirmed_early_wihtdrawal_activity";
    public static final String PARAM_FATCA_ERROR = "fatca_error";

    public static final String PARAM_NATIONALITY_ERROR = "param_nationality_error";
    public static final String PARAM_SESSION_ID = "session_id";
    public static final String PARAM_REASONS = "reasons_data";
    public static final String PARAM_TYPE_NO_CERTIFICATE = "type_no_certificate";
    public static final String PARAM_CONTENT_TYPE = "content_type";
    public static final String PARAM_COUNTRY_OF_BIRTH_CODE = "country_code";
    public static final String PARAM_HAS_INFO_RECORD = "info_record";
    public static final String PARAM_US_CITIZEN = "us_citizen";
    public static final String PARAM_TAX_OBLIGATIONS = "tax_obligations";
    public static final String PARAM_CIS_NUMBER = "tax_obligations";
    public static final String TEL = "tel:";
    public static final String MONEY_REQUEST_DETAIL = "money_request_detail";
    public static final String PHONE_CONTACT_BENEFICIARY_LIST = "phone_contact_beneficiary_list";
    public static final String SEND_MONEY_REQUESTS = "param_send_money_requests";
    public static final String NGI_TAX_DECLARATION = "tax_declaration";
    public static final String ENROLL_V2_LANDING = "enroll_v2_landing";
    public static final String LOGIN_WITHOUT_NEDBANK_ID = "login_without_nedbank_id";
    public static final String KEY_PASSWORD = "password";

    public static final String PARAM_IS_SECOND_LOGIN = "param_is_second_login";
    public static final String YOUR_BANKER = "YOUR_BANKER";
    public static final String SHOW_FEEDBACK = "SHOW_FEEDBACK";
    public static final String REPORT_FRAUD = "REPORT_FRAUD";
    public static final String TAG_YOUR_BANKER = "tag_your_banker";
    public static final String TAG_YOUR_BANKER_SUCCESS = "tag_your_banker_success";
    public static final String PARAM_CONTACT_US_FEATURE = "CONTACT US FEATURE";
    public static final String BOOKING_AN_APPOINTMENT = "BOOK_AN_APPOINTMENT";
    public static final String IS_CELL_UPDATE_API_CALL_NEEDED = "IS_CELL_UPDATE_API_CALL_NEEDED";
    public static final String IS_PROFILE_LIMIT_API_CALL_NEEDED = "IS_PROFILE_LIMIT_API_CALL_NEEDED";
    public static final String IS_ENROLLMENT_SUCCESS = "IS_ENROLLMENT_SUCCESS";
    public static final String PARAM_NTF = "ntf";
    public static final String IS_DEEPLINK = "isDeepLink";
    public static final String PARAM_NTF_SECOND_LOGIN = "ntf_second_login";
    public static final String PARAM_JUST_SAVE_T_AND_C_ACCEPTED = "is_just_save_t_and_c_accpeted";
    public static final String PARAM_MOVE_TO_LOGIN = "param_move_to_login";
    public static final String PARAM_MOVE_TO_HOME_FROM_DEEPLINK = "param_move_to_home_from_deeplink";
    public static final String PARAM_NTF_INVESTMENT_TYPE = "param_investment_type";
    public static final String PARAM_NTF_PRODUCT = "param_investment_product";
    public static final String PARAM_NTF_AGE = "param_age";
    public static final String NTF_EVERYDAY_INVESTMENTS = "everyday_investments";
    public static final String NTF_TAX_FREE_INVESTMENTS = "tax_free_investments";
    public static final String IS_FROM_NTF = "is_from_NTF";
    public static final String PARAM_IS_FROM_SAVE_AND_INVEST = "is_from_save_and_invest";

    public static final String NTF_AGE_GROUP_ACTIVITY = "ntf_age_group_Activity";
    public static final String NTF_YOUR_APP_ACTIVITY = "ntf_your_app_Activity";
    public static final String NTF_WEBVIEW_ACTIVITY = "ntf_webview_Activity";
    public static final String PERSONAL_LOAN_DETAILS_ACTIVITY = "personal_loan_details_activity";
    public static final String FICA_ERROR_SCREEN = "fica_error_screen";
    public static final String NTF_LOGIN_ACTIVITY = "ntf_login_Activity";
    public static final String NTF_SIGNUP_ACTIVITY = "ntf_signup_Activity";
    public static final String INVONLINE_NOW_ACTIVITY = "investment_online_now_activity";
    public static final String INVONLINE_NOW_TP_ACTIVITY = "investment_online_now_tp_activity";
    public static final String INVONLINE_NOW_EDUCATION_ACTIVITY = "investment_online_now_education_activity";
    public static final String INVONLINE_NOW_TP_RESTRICTION_ACTIVITY = "investment_online_now_tp_restriction_activity";
    public static final String TARGET_INVONLINE_PAYOUT_DETAIL = "investment_online_payout_detail";
    public static final String TARGET_INVONLINE_MATURING_DEPOSIT = "investment_online_maturing_detail";
    public static final String TARGET_INVONLINE_MATURING_EDU_PAGE = "investment_online_maturing_edu_page";
    public static final String TARGET_INVONLINE_RECURRING_DETAILS = "investment_online_recurring_detail";
    public static final String TARGET_INVONLINE_MANAGE_INVESTMENTS = "investment_online_manage_investments";
    public static final String TARGET_INVONLINE_DELETE_ENTRY = "investment_online_delete_flow_detail";
    public static final String INVONLINE_FIRST_WITHDRAWAL_DATE = "invonline_first_withdrawal_date";
    public static final String NOW_DETAIL_ACTIVITY = "now_detail_activity";
    public static final String EARLY_WITHDRAW_DONE_ACTIVITY = "early_withdraw_done_activity";
    public static final String NOW_DONE_ACTIVITY = "now_done_activity";
    public static final String NOW_CANCEL_ACTIVITY = "now_cancel_activity";
    public static final String INVONLINE_NOTICE_PRODUCT = "invonline_notice_product";
    public static final String BANK_LIST = "bank_list";
    public static final String TARGET_ONIA_FIAS_DISCLAIMER = "inv_online_fias_disclaimer_activity";
    public static final String ONIA_PRODUCT_TYPE = "InvestmentProductType";
    public static final String TARGET_ONIA_INVESTMENT_ERROR = "onia_investment_error";
    public static final String TARGET_NGI_MINOR_ERROR = "ngi_minor_error";
    public static final String TARGET_FATCA_NON_COMPLIANT_ERROR = "fatca_non_compliant_error";
    public static final String KEY_ONIA_ERROR_TYPE = "onia_invalid_client_type_key";
    public static final String KEY_RECIPIENT_SELECTED = "key_recipient_selected";
    public static final String KEY_IS_FROM_NOTICE_OF_WITHDRAWAL = "key_is_from_notice_of_widrawal";
    public static final String KEY_IS_ON_EDIT_SUCCESS_NOTICE = "key_is_on_edit_success_notice";
    public static final String KEY_INVESTMENT_ONLINE_IS_DELETE_NOTICE_ENABLED = "key_investment_online_is_delete_enabled";

    public static final String NTF_CANCEL_APPLICATION_ACTIVITY = "ntf_cancel_Activity";
    public static final String APPLICATION_STATUS_ACTIVITY = "application_status_activity";
    public static final String NTF_CANCEL_REASON_ACTIVITY = "ntf_cancel_reason_Activity";

    public static final String KEY_IO_INV_SWITCH_PENDING_NOTICE_COUNT = "key_io_inv_switch_pending_notice_count";
    public static final String KEY_ITEM_ACCOUNT_ID = "key_item_account_id";
    public static final String KEY_ITEM_ACCOUNT_NUMBER = "key_item_account_number";
    public static final String KEY_ITEM_ACCOUNT_NAME = "key_item_account_name";
    public static final String KEY_ITEM_RECEIPIENT_BANK_TYPE = "key_item_receipient_bank_type";
    public static final String KEY_ITEM_RECEIPIENT_NAME = "key_item_receipient_name";
    public static final String KEY_ITEM_RECEIPIENT_ACC_NUMBER = "key_item_receipient_acc_number";
    public static final String KEY_ITEM_RECEIPIENT_RECEIVEBANK = "key_item_receipient_receivebank";
    public static final String NOW_DELETE_ACTIVITY = "now_delete_activity";
    public static final String PARAM_ONIA_CLIENT_TYPE = "client_type_for_open_new_inv_account";
    public static final String PARAM_ONIA_SEC_OFFICER_CD = "sec_officer_cd_open_new_inv_account";
    public static final String PARAM_INVEST_AGE = "investor_age_inv_account";
    public static final String PARAM_INVEST_DCARNUMBER = "invest_dcarnum_inv_account";
    public static final String PARAM_ONIA_CIS_NUMBER = "cis_number_open_new_inv_account";
    public static final String PARAM_ONIA_BIRTH_DATE = "birth_date_for_open_new_inv_account";
    public static final String PARAM_INVONLINE_FIRST_CONVERSION_DATE = "invonline_first_conversion_date";
    public static final String PARAM_ONIA_SELECTED_ACCESS_TYPE = "invol.selected.access.type";
    public static final String PARAM_INVONLINE_SWITCHING_DATE = "invonline_switching_date";
    public static final String POSITON = "position";
    public static final String IS_EDIT = "edit_juristic_trading_address";
    public static final String ACTION = "action";
    public static final String SCALE = "scale";
    public static final String LOGIN_DATA = "login_data";
    public static final String DISABLE_BANK_CARD = "disable_bank_card";
    public static final String POSITION = "position";
    public static final String IS_ENABLED = "is_enabled";
    public static final String PAY_ACCOUNTS = "pay_accounts";
    public static final String IS_FROM_PAY_ME = "is_from_pay_me";
    public static final String TARGET_MATURING_INVESTMENTS = "target_maturing_investments";
    public static final String TARGET_MANAGE_INVESTMENT_VIEW = "target_manage_my_investments";
    public static final String TARGET_RIGHT_OPTIONS = "inv_online_right_options";
    public static final String TARGET_SUGGESTION_ACTIVITY = "inv_suggestions";
    public static final String TARGET_HELP_ME_FIND_GUARANTEED_SAVINGS_ACTIVITY = "inv_help_me_find_guaranteed_savings";
    public static final String TARGET_MPA_QUESTIONS = "ntf_mpa_questions";
    public static final String TARGET_ONIA_EDU_PAGE = "inv_online_onia_edu_page";
    public static final String KEY_IS_FROM_UPDATE_NOTICE_OF_WITHDRAWAL = "key_is_from_update_notice_of_widrawal";
    public static final String KEY_IS_FROM_AVS_FAILED = "key_is_from_avs_failed";
    public static final String KEY_IS_FROM_AVS_FAILED_ACC_NO = "key_is_from_avs_failed_acc_no";
    public static final String KEY_IS_FROM_AVS_FAILED_BANK_SORT_CODE = "key_is_from_avs_failed_bank_sort_code";

    public static final String TARGET_NGI_GET_STARTED = "inv_online_ngi_get_started";
    public static final String TARGET_INVESTMENT_OPTION_LANDING = "inv_option_landing";
    public static final String TARGET_NGI_COUNTRY_LIST = "inv_online_ngi_country_list";
    public static final String INVONLINE_VIEW_PAYOUT = "investment_online_view_payout_target";
    public static final String INVONLINE_VIEW_REINVEST = "investment_online_view_reinvest_target";
    public static final String TARGET_INVONLINE_INVESTMENT_SWITCHING = "investment_online_inv_switching_target";
    public static final String TARGET_INVONLINE_DELETE_SWITCHING = "investment_online_inv_delete_switching_enable";
    public static final String TARGET_INVONLINE_INV_SWITCH_ACCOUNT_DETAILS = "investment_online_inv_switch_account_details_target";
    public static final String TARGET_INVONLINE_ACCOUNT_DETAILS = "investment_online_account_details_target";
    public static final String TARGET_INVONLINE_EARLY_WITHDRAWAL_PENALTY_DETAILS = "investment_online_early_withdrawal_penalty_details_target";
    public static final String TARGET_INVONLINE_EARLY_WITHDRAWAL_AMOUNT = "investment_online_early_withdrawal_amount_target";
    public static final String TARGET_INVONLINE_EARLY_WITHDRAWAL_PAYOUT_DATE = "investment_online_early_withdrawal_payout_date_target";
    public static final String TARGET_INVONLINE_INV_SWITCH_DEPOSIT_DETAILS = "investment_online_inv_switch_deposit_details_target";
    public static final String TARGET_INVONLINE_INV_SWITCH_PENDING_NOTICES = "investment_online_inv_switch_pending_notices_target";
    public static final String TARGET_IO_INV_SWITCH_PENDING_NOTICES_REVIEW = "investment_online_inv_switch_pending_notices_review_target";
    public static final String TARGET_IO_INV_SWITCH_DELETE_PENDING_NOTICES = "investment_online_inv_switch_delete_pending_notices_target";
    public static final String TARGET_IO_AVS_ACTIVITY = "investment_online_avs_activity";
    public static final String PARAM_VIEW_PAYOUT_ITEM_ACCOUNT_ID = "investment_online_view_payout_item_account_id";
    public static final String PARAM_VIEW_PAYOUT_ITEM_ACCOUNT_NAME = "investment_online_view_payout_item_account_name";
    public static final String PARAM_VIEW_PAYOUT_ITEM_ACCOUNT_NUMBER = "investment_online_view_payout_item_account_number";
    public static final String PARAM_VIEW_PAYOUT_DELETE_PAYOUT_ENABLE = "investment_online_view_payout_delete_payout_enable";
    public static final String PARAM_VIEW_REINVEST_ITEM_NOTICE_PRODUCT = "investment_online_view_reinvest_item_isnotice";
    public static final String PARAM_VIEW_REINVEST_ADDITIONAL_PROJECTED_AMOUNT = "investment_online_view_reinvest_additional_Projected_amount";
    public static final String PARAM_SWITCHING_INVESTMENT_VIEW_MODEL = "inv_online_switching_view_model";
    public static final String INTEREST_RATES_SCREEN = "interest_rates_screen";
    public static final String TARGET_ACCOUNT_TYPE_LIST = "account_type_list";
    public static final String PARAM_DELETE_REINVEST_ENABLED = "investment_online_reinvest_enabled";
    public static final String IS_FROM_ADAPTER = "is_from_adapter";
    public static final String IS_FROM_APO = "is_from_apo";
    public static final String IS_FROM_INVESTMENT_ONLINE = "is_from_investment_online";

    public static final String PARAM_IS_HL_REQUEST = "hl_request";
    public static final String KEY_MOBILE_NUMBER = "mobile_number";
    public static final String HOME_LOAN_TOOLKIT = "home_loan_toolkit";
    public static final String FINANCIAL_PLANNER_HOME = "financial_planner_home";
    public static final String FINANCIAL_PLANNER_FORM = "financial_planner_form";
    public static final String PERSONAL_LOAN_HOME = "personal_loan_home";
    public static final String CHAT_OPTION_SCREEN = "chat_option_screen";
    public static final String CARD_ENTRY = "card_entry";
    public static final String PRE_APPROVED_OFFERS_NOTIFICATIONS = "pre_approved_offers_notifications";

    public static final String TARGET_NOTIFICATION_NAVIGATION = "notification_navigation";
    public static final String TARGET_NOTIFICATION_CENTER = "notification_center";
    public static final String TARGET_NOTIFICATION_DETAILS = "notification_details";
    public static final String TARGET_AJO_NOTIFICATION_DETAILS = "ajo_notification_details";

    public static final String TARGET_GLOBAL_NAVIGATION_HANDLER = "global_navigation_handler";
    public static final String TARGET_NOTIFICATION_INVITE_AND_ACCEPT_FAMILY_BANKING = "notification_invite_and_accept_family_banking";
    public static final String TARGET_NOTIFICATION_INVITE_AND_OPEN_ACCOUNT_FAMILY_BANKING = "notification_invite_and_open_account_family_banking";

    public static final String PARAM_LIFESTYLE_IS_FROM_CHAT_SETTING = "lifestyle_is_from_chat_setting";
    public static final String TARGET_NOTIFICATION_MESSAGES = "notification_messages";
    public static final String TARGET_NOTIFICATION_TRANSACTION = "notification_transaction";
    public static final String TARGET_NOTIFICATION_ERROR = "notification_error";
    public static final String TARGET_NOTIFICATION_TRANSACTION_DETAILS = "notification_transaction_details";
    public static final String TARGET_NOTIFICATION_PREFRENCES = "notification_prefrences";
    public static final String TARGET_NOTIFICATION_DELIVERY_PREFRENCES = "notification_delivery_prefrences";
    public static final String TARGET_NOTIFICATION_PREFRENCES_ALL_ACCOUNTS = "notification_all_accounts_prefrences";
    public static final String TARGET_NOTIFICATION_PREFRENCES_ACCOUNT = "notification_account_prefrences";
    public static final String TARGET_NOTIFICATION_REDIRECT_URL = "notification_redirect_url";
    public static final String TARGET_NOTIFICATION_CONTEXT_SWITCH_CONFIRM = "context_switch_confirm";
    public static final String TARGET_NOTIFICATION_ENABLE = "notification_anable";
    public static final String NID_FORGOT_COUNTRY_SELECTION = "nid_forgot_country_selection";

    public static final String TARGET_STATE_SELECTION = "target_state_selection";
    public static final String TARGET_GPAY_INRODUCTION = "gpay_introduction";

    public static final String FRAUD_AWARENESS_CAMPAIGN = "fraud_awareness_campaign";
    public static final String TARGET_TRANSACTION_NOTIFICATION_REPORT_FRAUD = "transaction_notification_report_fraud";
    public static final String TARGET_TRANSACTION_NOTIFICATION_REPORT_FROZEN = "transaction_notification_report_frozen";

    public static final String TARGET_FAIS_EVERDAY_BANKING = "edb_fais";
    public static final String TARGET_EFICA_CAMERA_PERMISSION_EVERDAY_BANKING = "edb_camera_permission_efica";
    public static final String TARGET_DISCLAIMER_EVERDAY_BANKING = "edb_disclaimer";

    public static final String KEY_APPROVE_IT_VERIFICATION_ID = "approve_it_verification_id";
    public static final String HL_SETTLEMENT_REQUEST = "hl_settlement_request";
    public static final String EDIT_PROFILE_LIMIT = "edit_profile_limit";
    public static final String EDIT_LIMIT_GP_FLOW = "edit_limit_gp_flow";
    public static final String PARAM_PROFILE_LIMIT_ID = "profile_limit_id";
    public static final String PARAM_PROFILE_LIMIT_LIST = "profile_limit_list";
    public static final String KEY_APPROVE_IT_TYPE = "key_approve_it_type";
    public static final String VAS = "vas";
    public static final String VAS_TERM_AND_CONDITIONS = "vas_term_and_condition";
    public static final String VAS_TOGGLE_OFF = "vas_toggle_off";
    public static final String VAS_FEATURE_UNAVAILABLE = "vas_feature_unavailable";
    public static final String IS_SECOND_LOGIN = "is_second_login";
    public static final String APP_UPDATE_TYPE = "app_update_type";
    public static final String IS_OPEN_ON_PREAPPROVED_OFFER_CLICK = "isOpenOnPreApprovedOfferClick";

    public static final String UPDATE_APP_TITLE = "update_app_title";
    public static final String UPDATE_APP_DESC = "update_app_desc";

    public static final String IS_NON_TP_ERROR = "isNonTPError";

    public static final String IS_SKIP_LANDING = "is_skip_landing";
    public static final String IS_FROM_NFP = "is_from_nfp";
    public static final String PARAM_LOCKED_PROFILE_USERNAME = "lockedUserName";
    public static final String CHANGE_PASSWORD_SUCCESS = "change_password_success";
    public static final String AGENT_DESK = "agent_desk";
    public static final String ADDITIONAL_INFORMATION = "additional_information";
    public static final String AGENT_CONNECTED_MESSAGE = "agent_connected_message";
    public static final String MORE_REPORT_FRAUD = "more_report_fraud";
    public static final String REPORT_FRAUD_SUCCESS = "report_fraud_success";
    public static final String REPORT_FRAUD_FAILURE = "report_fraud_failure";
    public static final String REPORT_FRAUD_MAX_ATTEMPTS = "report_fraud_max_attempts";
    public static final String SUSPICIOUS_REASONS = "suspicious_reasons";

    public static final String MANUAL_ACCOUNT_LINKING = "manual_account_linking";
    public static final String KB_AUTO_ACCOUNT_LINKING = "auto_account_linking";
    public static final String PARAM_PRE_LOGIN = "is_pre_login";
    public static final String PARAM_PRE_LOGIN_DEEP_LINK = "pre_login_deep_link";
    public static final String CMS_MEDIA_CONTENT = "cms_media_content";
    public static final String SHARE_PROOF_OF_PAYMENT_METHOD = "share_pop_method";
    public static final String NEW_SHARE_PROOF_OF_PAYMENT_METHOD = "new_share_proof_of_payment_method";
    public static final String NEW_SHARE_PROOF_OF_PAYMENT = "new_share_proof_of_payment";
    public static final String TRANSACTION_HISTORY_DETAIL = "transaction_history_detail";
    public static final String SHARE_PROOF_OF_PAYMENT_API_RESPONSE = "share_pop_api_response";
    public static final String RECIPIENT_DETAIL_WITH_HISTORY = "recipient_detail_with_history";
    public static final String TERMS_AND_CONDITIONS_SHARE = "terms_condition_share";
    public static final String CREDIT_LIMIT_INCREASE_ENTRY = "credit_limit_increase_entry";
    public static final String PARAM_IS_FIRST_LOGIN = "PARAM_IS_FIRST_LOGIN";

    public static final String SELECT_STATEMENT_OR_DOCUMENT_OPTION = "select_statement_or_document_option";

    public static final String FEATURE_CARD_DETAIL = "feature_card_detail";

    public static final String EXPENDED_CONTENT_PAGE = "expended_content_page";

    public static final String QUICK_PAY = "quick_pay";
    public static final String PAY_ME = "pay_me";
    public static final String COVID_19 = "covid_19";

    public static final String MONEY_TRACKER_FLOW_NAVIGATION_ACTIVITY = "money_tracker_flow_navigation_activity";
    public static final String DEMO_LANDING = "demo_landing";
    public static final String TRAVEL_CARD_SELL_CURRENCY = "TRAVEL_CARD_SELL_CURRENCY";
    public static final String TRAVEL_CARD_SELL_AMOUNT = "TRAVEL_CARD_SELL_AMOUNT";
    public static final String EXIT_DEMO_DIALOG = "exit_demo_dialog";
    public static final String REGISTRATION_DEMO_DONE = "registration_demo_done";
    public static final String PARAM_TRANS_PUSH_DATA = "trans_push_data";
    public static final String PARAM_IS_FROM_WIDGET = "param_is_from_widget";
    public static final String PARAM_IS_FROM_FEATURE = "param_is_from_feature";
    public static final String HOME_LOAN_SUBURB_SEARCH = "home_loan_suburb_search";

    public static final String PARAM_IS_NO_TAB_VISIBLE = "param_atm_and_branch_no_tab_screen";
    public static final String PARAM_BRANCH_SELECTED_APPLY_SBS = "param_branch_selectde_sbs";
    public static final String PARAM_BRANCH_ADDRESS_SELECTED_APPLY_SBS = "param_branch_address_selectde_sbs";
    public static final String APPLY_CONFIRMBRANCHDETAIL = "apply_branchdetail_activity";

    public static final String APPLY_TYPE = "apply_type_activity";
    public static final String SELECTED_BRANCH_SBS_APLY = "selected_branch";
    public static final String SELECTED_BRANCH_ADDRESS_SBS_APLY = "selected_branch_address";
    public static final String IS_PRE_LOGIN_SBS_APPLY = "pre_login";

    //retention
    public static final String RETENTION_FEEDBACK_SCREEN = "retention_feedback_screen";
    public static final String RETENTION_FEEDBACK_SUCCESS = "retention_feedback_success";
    public static final String RETENTION_WELCOME_ACTIVITY = "retention_welcome_activity";
    public static final String RETENTION_MULTIPLE_SHARE_ACCOUNT_ACTIVITY = "retention_multiple_share_account_activity";
    public static final String RETENTION_TASK_SELECTION_ACTIVITY = "retention_task_selection_activity";
    public static final String RETENTION_INFO_ACTIVITY = "retention_info_activity";
    public static final String RETENTION_TASK_SELECTION_NOTIFICATION_JOURNEY = "retention_task_selection_notification_journey";
    public static final String IS_IN_RETENTION_DEBIT_ORDER_FLOW = "is_in_retention_debit_order_flow";

    public static final String BILL_PAYMENT_CHOOSE_CARD = "bill_payment_choose_card";

    public static final String LANDING_LOANS = "landing_loans";
    public static final String NEW_APPOINTMENT = "new_appointment";
    public static final String MANAGE_APPOINTMENT = "manage_appointment";
    public static final String TARGET_SWITCHING_DEPOSIT_DETAILS = "switching_deposit_detail_activity";
    public static final String TARGET_SWITCHING_REVIEW = "switching_review";
    public static final String TARGET_SWITCHED_INVESTMENT = "switched_investment";
    public static final String TARGET_DELETE_SWITCHED_INVESTMENT = "delete_switched_investment";
    public static final String TARGET_DELETE_SWITCHED_INV_ERROR = "delete_switched_error";
    public static final String TARGET_SWITCHING_CONGRATULATIONS = "switching_congratulations";
    public static final String INV_SWITCHING_DASHBORAD = "nvol_dash2";
    public static final String SWITCH_FEATURE_SELECT = "switch_select";
    public static final String TARGET_OFFERS_PREFERENCE_SCREEN = "target_offers_preference_screen";
    public static final String TARGET_OFFERS_PREFERENCE_LIST_SCREEN = "target_offers_preference_list_screen";

    public static final String RESTRICTED_TRANSACTION_SCREEN = "restricted_transaction_screen";
    public static final String PARAM_SHOW_BACK_BUTTON = "param_show_back_button";
    public static final String OPEN_PERSONAL_BANK_INTENT = "bank_intent";
    public static final String PARAM_BUY_INTENT = "param_buy_intent";

    public static final String BORROW_INTENT_ACTIVITY = "borrow_intent_activity";

    public static final String CONVO_CHAT_SCREEN = "convo_chat_screen";
    public static final String CHATBOT_INTRODUCTION_SCREEN = "chatbot_intoduction_screen";
    public static final String SETTINGS = "settings";
    public static final String PROFILE_LIMITS = "profile_limits";
    public static final String PROFILE_LIMITS_BUSINESS_USER = "profile_limits_business_user";
    public static final String PROFILE_DEREGISTER_KIDS_BANKING = "profile_deregister_kids_banking";
    public static final String PROFILE_DELINK_KIDS_BANKING = "child_profile_delink_kids_banking";

    public static final String KEY_FROM_CONVOCHATBOT = "fromconvochatbot";
    public static final String PARAM_IS_CONTEXT_SWITCHED = "is_context_switched";
    public static final String TARGET_ITA_AUTHENTICATION_SCREEN = "ita_authentication_screen";

    public static final String LOCKER_MAP = "locker_map";
    public static final String CARD_DELIVERY_OPTIONS = "card_delivery_options";
    public static final String CARD_DELIVERY_BRANCH_CONFIRMATION = "card_delivery_branch_confirmation";
    public static final String CARD_DELIVERY_LOCKER_CONFIRMATION = "card_delivery_locker_confirmation";
    public static final String CARD_DELIVERY_DELIVER_TO_ME_CONFIRMATION = "card_delivery_deliver_to_me_confirmation";
    public static final String CARD_DELIVERY_RESULT = "card_delivery_result";
    public static final String PARAM_CARD_DELIVERY_FLOW = "param_card_delivery_flow";
    public static final String VALUE_CARD_DELIVERY_FLOW_BLOCK_AND_REPLACE = "BlockAndReplace";
    public static final String VALUE_CARD_DELIVERY_FLOW_EFICA = "Efica";
    public static final String PARAM_IS_CARD_ORDERING_SUCCESS = "is_card_ordering_success";

    public static final String PARAM_SA_ID = "param_sa_id";
    public static final String PARAM_INPUT_DATA = "param_input_data";

    public static final String BILL_PAYMENT_EDIT_SCHEDULED = "bill_payment_edit_scheduled";
    public static final String BILL_PAYMENT_EDIT_REVIEW_BILL_SCHEDULED = "bill_payment_edit_review_bill_scheduled";
    public static final String BILL_PAYMENT_DETAIL = "bill_payment_detail";
    public static final String BILL_PAYMENT_STATUS = "bill_payment_status";
    public static final String BILL_PAYMENT_ALERT_TYPE = "bill_payment_alert_type";
    public static final String HISTORY_SEARCH_SCREEN = "history_search_screen";
    public static final String PARAM_IS_FICA_ON_CARD_DELIVERY = "is_fica_on_card_delivery";
    public static final String PREAPPROVED_OFFERS_APPLY_ACTIVITY = "preapproved_offers_od_apply_activity";

    public static final String NFW_DASHBOARD_ACTIVITY = "nfw_dashboard_activity";
    public static final String FATCA_RESTRICTION_MESSAGE_SCREEN = "fatca_restriction_message_screen";
    public static final String SENSITIVE_TRANCTION_MESSAGE_SCREEN = "sensitive_transaction_message_screen";
    public static final String CREDIT_CARD_CHAT_ENTRY_ACTIVITY = "credit_card_chat_entry_activity";

    public static final String UNBOXING_ACTIVITY = "unboxing_activity";

    public static final String CARD_TAB_SELECTED = "card_tab_selected";
    public static final String IS_FROM_HOME = "is_from_home";
    public static final String IS_RETAIL = "is_retail";

    //send cash
    public static final String SEND_CASH_LANDING = "send_cash_landing";
    public static final String NEW_RECIPIENT = "new_recipient";
    public static final String PARTICIPATING_STORES = "participating_stores";
    public static final String SEND_CASH_PAYEMNT = "send_cash_payemnt";
    public static final String SEND_CASH_PAYMENT_REVIEW = "send_cash_payment_review";
    public static final String SEND_CASH_PAYMENT_SUCCESS = "send_cash_payment_success";
    public static final String SEND_MONEY_TRASACTION_HISTORY = "send_cash_transaction_history";
    public static final String SEND_MONEY_TRASACTION_DETAILS = "send_cash_transaction_DETAILS";
    public static final String HOW_SEND_MONEY_WORKS = "how_send_cash_works";
    public static final String SEND_MONEY_TNC = "send_money_tnc";
    public static final String RPP_TNC = "rpp_tnc";
    public static final String IDVL_VERIFY_ME = "idvl_verify_me";
    public static final String CONVERSATION_ID = "conversation_id";
    public static final String STATEMENT_DECLARATION_SCREEN = "state_declaration_screen";

    public static final String NAVIGATION_FROM = "navigation_from";
    public static final String AVS_ENABLE_BANK = "avs_enable_bank";
    public static final String MIN_WITHDRAWL_NOTICE = "min_withdrawl_notice";

    //kids banking registration
    public static final String UPDATED_TNC_KIDS_SCREEN = "updated_tnc_minor_screen";
    public static final String FRAUD_ALERT_KIDS_SCREEN = "fraud_alert_kids_screen";
    public static final String KIDS_BANKING_PARENT_REGISTRATION_ACTIVITY = "kids_banking_parent_registration_activity";
    public static final String KIDS_BANKING_CHILD_REGISTER_ACTIVITY = "kids_banking_child_register_activity";
    public static final String KIDS_API_ERROR_SCREEN_ACTIVITY = "kids_api_error_screen_activity";
    public static final String PARAM_PARTY_RELATIONSHIP_RESPONSE_VIEW_MODEL = "party_relationship_response_view_model";
    public static final String LOC_TRANSACTION_ERROR_SCREEN = "loc_transaction_error_screen";
    public static final String KIDS_BANKING_FLOW_ON_NEDBANK4ME_SUCCESS = "kids_banking_flow_on_nedbank4me_success";
    public static final String IS_KB_REG_FROM_LOGIN_SCREEN = "from_login_screen";
    public static final String GENERIC_TECHNICAL_ERROR_SCREEN = "generic_technical_error_screen";
    public static final String ENTRY_POINT_FOR_INTERNATION_PAYMENT = "entry_point_for_internation_payment";
    public static final String KIDS_ACK_TERMS_AND_CONDITION_SCREEN = "kid_ack_terms_and_condition_screen";
    public static final String KIDS_ACK_TERMS_AND_CONDITION_PARAM = "kid_ack_terms_and_condition_screen";
    public static final String PARENT_ACK_FRAUD_AWARENESS_SCREEN = "parent_ack_fraud_awareness_screen";
    public static final String KIDS_ACK_FRAUD_AWARENESS_PARAM = "kids_ack_fraud_awareness_param";


    //Disc renewal registration
    public static final String DISC_RENEWAL_INTRODUCTION = "disc_renewal_introduction";
    public static final String VEHICLES_LANDING = "vehicles_landing";
    public static final String CHOOSE_VEHICLES = "choose_vehicles";
    public static final String VEHICLE_DETAILS = "vehicle_details";
    public static final String FEES_PAYABLE = "fees_payable";
    public static final String DELIVERY_DETAILS = "delivery_details";
    public static final String DISCS_RENEWAL_PAYMENT = "discs_renewal_payment";
    public static final String DISCS_RENEWAL_REVIEW = "discs_renewal_review";
    public static final String DISCS_RENEWAL_SUCCESS= "discs_renewal_success";
    public static final String DISCS_RENEWAL_HISTORY= "discs_renewal_history";
    public static final String DISC_TNC= "discs_TNC";

    //Traffic fines registration

    public static final String TRAFFIC_FINES_DUE= "traffice_fines_due";
    public static final String TRAFFIC_FINES_DETAILS= "traffice_fines_details";
    public static final String TRAFFIC_FINES_REVIEW= "traffice_fines_review";
    public static final String TRAFFIC_FINES_SUCCESS= "traffice_fines_success";

    public static final String TRAFFIC_FINES_HISTORY_DETAILS= "traffice_fines_history_details";

    public static final String PARAM_DISC_RENEWAL_VEHICLE_DETAILS = "param_disc_renewal_vehicle_details";
    public static final String IS_LICENSE_SINGLE_VEHICLE = "is_single_vehicle";
    public static final String DISCS_RENEWAL_VEHICLES_DETAILS = "discs_renewal_vehicles_details";
    public static final String DISCS_RENEWAL_TRANSACTION_DETAILS = "discs_renewal_transaction_details";

    public static final String PARAM_IS_TRAFFIC_FINE_ALERT = "param_is_traffic_fine_alert";

    public static final String TNC_NOTICE_TYPE= "tnc_notice_type";
    public static final String IS_SINGLE_FINE = "is_single_fine";
    public static final String PARAM_TRAFFIC_FINES_DETAILS = "param_TRAFFIC_FINES_details";
    // Doc upload
    public static final String DOC_UPLOAD_FLOW = "doc_upload_flow";
    public static final String UPLOAD_DATA = "upload_data";
    public static final String UPLOAD_DATA_RESULT = "upload_data_result";

    public static final String REWARDS_TNC = "rewards_tnc";
    public static final String REWARDS_LANDING = "rewards_landing";
    public static final String PARAM_RRB_USER = "rrb_user";
    public static final String PARAM_IS_RRB_PRIVATE_TP_ACCOUNT = "is_rrb_private_tp_account";
    public static final String PAY_REVIEW = "pay_review";
    private final String target;
    private final Map<String, Object> params = new HashMap<>();
    private Long id;
    private boolean clearStack = false;
    private boolean intentFlagClearTopWithSingleTop;
    private boolean intentSingleTop;
    private boolean intentFlagClearTop;
    private boolean isForwardActivityResult;
    private boolean passAllData;
    private boolean intentFlagNewTask;

    public static final String PARAM_BLACKLISTED_PACKAGE_NAME = "blacklisted_package_name";

    public static final String SALES_LANDING = "sales_landing";

    protected NavigationTarget(final String target) {
        this.target = target;
    }

    public static NavigationTarget to(final String target) {
        return new NavigationTarget(target);
    }

    public NavigationTarget withId(final long id) {
        this.id = id;
        return this;
    }

    public String getTarget() {
        return target;
    }

    public NavigationTarget withParam(final String name, final String value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final String name, final CharSequence value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final String name, final boolean value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final String name, final long value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final String name, final int value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final String name, final Parcelable value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final String name, final double value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final String name, final Serializable value) {
        params.put(name, value);
        return this;
    }

    public NavigationTarget withParam(final Serializable name, final Serializable value) {
        params.put(name.toString(), value);
        return this;
    }

    public NavigationTarget withParam(final String name, final List<? extends Parcelable> value) {
        params.put(name, value);
        return this;
    }
    public NavigationTarget withClearStack(final boolean clearStack) {
        this.clearStack = clearStack;
        return this;
    }

    public NavigationTarget withIntentFlagClearTopSingleTop(final boolean intentFlagClearTopSingleTop) {
        this.intentFlagClearTopWithSingleTop = intentFlagClearTopSingleTop;
        return this;
    }

    public NavigationTarget withIntentForwardActivityResult(final boolean forwardActivityResult) {
        this.isForwardActivityResult = forwardActivityResult;
        return this;
    }

    public NavigationTarget withIntentSingleTop(final boolean intentSingleTop) {
        this.intentSingleTop = intentSingleTop;
        return this;
    }

    public NavigationTarget withIntentFlagClearTop(final boolean intentFlagClearTop) {
        this.intentFlagClearTop = intentFlagClearTop;
        return this;
    }

    public NavigationTarget withIntentFlagNewTask(final boolean intentFlagNewTask) {
        this.intentFlagNewTask = intentFlagNewTask;
        return this;
    }

    public NavigationTarget withAllData(final boolean withAllData) {
        this.passAllData = withAllData;
        return this;
    }

    public Long getId() {
        return id;
    }

    public String getStringParam(final String name) {
        return getTypedValue(name, String.class);
    }

    public Long getLongParam(final String name) {
        return getTypedValue(name, Long.class);
    }

    public Integer getIntParam(final String name) {
        return getTypedValue(name, Integer.class);
    }

    public Boolean getBooleanParam(final String name) {
        return getTypedValue(name, Boolean.class);
    }

    public Parcelable getParcelableParam(final String name) {
        return getTypedValue(name, Parcelable.class);
    }

    public Object getSerializableParam(final String name) {
        return getTypedValue(name, Object.class);
    }

    public ParceableList getParceableListParam(final String name) {
        return getTypedValue(name, ParceableList.class);
    }

    public Bundle getParams() {
        Bundle bundle = new Bundle();
        putParams(bundle);
        return bundle;
    }

    boolean shouldClearStack() {
        return clearStack;
    }

    boolean shouldClearTopWithSingleTop() {
        return intentFlagClearTopWithSingleTop;
    }

    boolean shouldSingleTop() {
        return intentSingleTop;
    }

    boolean shouldClearTop() {
        return intentFlagClearTop;
    }

    boolean forwardActivityResult() {
        return isForwardActivityResult;
    }

    boolean shouldNewTask() {
        return intentFlagNewTask;
    }

    boolean shouldPassAllData() {
        return passAllData;
    }

    private void putParams(Bundle bundle) {
        for (String key : params.keySet()) {
            Object obj = params.get(key);
            if (obj instanceof String) {
                bundle.putString(key, (String) obj);
            } else if (obj instanceof CharSequence) {
                bundle.putCharSequence(key, (CharSequence) obj);
            } else if (obj instanceof Long) {
                bundle.putLong(key, (Long) obj);
            } else if (obj instanceof Integer) {
                bundle.putInt(key, (Integer) obj);
            } else if (obj instanceof ParceableList) {
                //noinspection unchecked
                bundle.putParcelableArrayList(key, (ArrayList<? extends Parcelable>) obj);
            } else if (obj instanceof Parcelable) {
                bundle.putParcelable(key, (Parcelable) obj);
            } else if (obj instanceof Boolean) {
                bundle.putBoolean(key, (Boolean) obj);
            } else if (obj instanceof Serializable) {
                bundle.putSerializable(key, (Serializable) obj);
            }
        }
    }

    private <T> T getTypedValue(String key, Class<T> tClass) {
        Object value = params.get(key);
        if (tClass.isAssignableFrom(value.getClass())) {
            //noinspection unchecked
            return (T) value;
        }
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        NavigationTarget that = (NavigationTarget) o;
        return target.equals(that.target);
    }

    @Override
    public int hashCode() {
        return target.hashCode();
    }

    public interface EXTRAS {
        String BANK_SELECTED = "bank_selected";
        String BRANCH_SELECTED = "branch_selected";
    }

    public interface EXTRAS_BANK {
        String VIEW_PAGER_WTH_TAB_VIEW = "VIEW_PAGER_WTH_TAB_VIEW";
        String SELECTED_BANK_ID = "SELECTED_BANK_ID";
        String SELECTED_BANK_BRANCH_LIST = "SELECTED_BANK_BRANCH_LIST";
        String AVS_BANK_LIST_FLAG = "AVS_BANK_LIST_FLAG";
    }

    private static class ParceableList extends ArrayList<Parcelable> {
    }
}
